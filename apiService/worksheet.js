import api from "@/api";
import { Get, Post, Delete,  } from "@/common/request";
function worksheetCreate(param) {
  return Post({
    url: api.worksheetCreate,
    bodyParam: param,
  });
}
function worksheetList(param) {
  return Post({
    url: api.worksheetList,
    bodyParam: param,
  });
}
function worksheetDetail(worksheetId) {
  return Get({
    url: api.worksheetDetail,
    pathParam: { worksheetId },
  });
}

export default {
  worksheetCreate,
  worksheetList,
  worksheetDetail,
};
