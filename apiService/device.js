import api from "@/api";
import { Get, Post, Delete, } from "@/common/request";

function devicePage(param) {
  return Post({
    url: api.devicePage,
    bodyParam: param,
  });
}

function getFailureList(param) {
  return Post({
    url: api.getFailureList,
    bodyParam: param,
  });
}
function ignoreWarning(id) {
  return Get({
    url: api.ignoreWarning,
    pathParam: { id },
  });
}
function getByDeviceSN(stationNo) {
  return Get({
    url: `${api.getByDeviceSN}?stationNo=${stationNo}`
  });
}
function failureDetail(id) {
  return Get({
    url: api.failureDetail,
    pathParam: { id },
  });
}
function getCabinetsBySNAndSort(deviceSn, sort) {
  return Get({
    url: api.getCabinetsBySNAndSort,
    pathParam: { deviceSn, sort },
  });
}
function getCustomerStationSummary(supplierId) {
  let url = api.getCustomerStationSummary;
  if (supplierId) {
    url += `?supplierId=${encodeURIComponent(supplierId)}`;
  }
  return Get({
    url
  });
}
function statisticsDailyUsageAndProfit(params) {
  return Post({
    url: api.statisticsDailyUsageAndProfit,
    bodyParam: params,
  });
}

function getSevenStatisticsAll() {
  return Get({
    url: api.getSevenStatisticsAll,
  });
}
function getBatteryDetail(deviceSn, sort) {
  return Get({
    url: api.getBatteryDetail,
    pathParam: { deviceSn, sort },
  });
}

function getBatteryRackList(deviceSn) {
  return Get({
    url: api.getBatteryRackList,
    pathParam: { deviceSn },
  });
}

function getBatteryPackList(deviceSn, sort) {
  return Get({
    url: api.getBatteryPackList,
    pathParam: { deviceSn, sort },
  });
}

// function getDeviceWorkProcessList(deviceSn) {
//   return Get({
//     url: api.getDeviceWorkProcessList,
//     pathParam: { deviceSn },
//   });
// }

function getBatteryWorkProcessDetail(deviceSn, workProcessId) {
  return Get({
    url: api.getBatteryWorkProcessDetail,
    pathParam: { deviceSn, workProcessId },
  });
}
function statisticsSocAndWorkStatusByDay(deviceSn, day) {
  return Get({
    url: api.statisticsSocAndWorkStatusByDay,
    pathParam: { deviceSn, day },
  });
}

function getRecommendBatteryWorkProcess(deviceSn, workProcessId) {
  return Get({
    url: api.getRecommendBatteryWorkProcess,
    pathParam: { deviceSn, workProcessId },
  });
}

// function updateBatteryWorkProcess(param) {
//   return Post({
//     url: api.updateBatteryWorkProcess,
//     bodyParam: param,
//   });
// }

function getBatteryRack({ stationNo, containerNo }) {
  return Get({
    url: api.getBatteryRack,
    pathParam: { stationNo, containerNo }
  })
}

function deviceAlarmPage(param) {
  return Post({
    url: api.deviceAlarmPage,
    bodyParam: param
  })
}

function historyPage(param) {
  return Post({
    url: api.historyPage,
    bodyParam: param
  })
}

function clearAlarm(param) {
  return Post({
    url: `${api.clearAlarm}?alarmId=${param.alarmId}`
  })
}
function alarmTransfer(param) {
  return Post({
    url: api.alarmTransfer,
    bodyParam: param
  })
}


function getDetail(param) {
  return Get({
    url: `${api.getDetail}?alarmId=${param.alarmId}`
  })
}

function updateStationInfo(params) {
  return Post({
    url: api.updateStationInfo,
    bodyParam: params
  })
}
// 三方绑定
function bindThirdAccount(params) {
  return Post({
    url: api.bindThirdAccount,
    bodyParam: params,
    config: {
      needToken: false,
    },
  })
}

function getSubSupplierTree(supplierId, businessType) {
  return Get({
    url: api.getSubSupplierTree,
    pathParam: { supplierId, businessType: 'energy_storage_cabinet' },
  })
}
function statisticStationChargeAndProfitSummary(params) {
  return Post({
    url: api.statisticStationChargeAndProfitSummary,
    bodyParam: params
  })
}

function getWorkOrderPage(params) {
  return Post({
    url: api.getWorkOrderPage,
    bodyParam: params
  })
}

function getInspectionDevicePage(params) {
  return Post({
    url: api.getInspectionDevicePage,
    bodyParam: params
  })
}
function getInspectionLogPage(params) {
  return Post({
    url: api.getInspectionLogPage,
    bodyParam: params
  })
}
function addInspectionLog(params) {
  return Post({
    url: api.addInspectionLog,
    bodyParam: params
  })
}

function getWorkOrderDetail(id) {
  return Get({
    url: api.getWorkOrderDetail,
    pathParam: { id },
  })
}


function getOrgAndSubOrgStationNameList(orgId) {
  return Get({
    url: api.getOrgAndSubOrgStationNameList,
    pathParam: { orgId },
  })
}
function getStaffByRole(params) {
  return Post({
    url: api.getStaffByRole,
    bodyParam: params
  })
}
function manualCreate(params) {
  return Post({
    url: api.manualCreate,
    bodyParam: params
  })
}
function updateWorkOrder(params) {
  return Post({
    url: api.updateWorkOrder,
    bodyParam: params
  })
}

export default {
  getBatteryDetail,
  devicePage,
  getFailureList,
  ignoreWarning,
  getByDeviceSN,
  failureDetail,
  getCabinetsBySNAndSort,
  getCustomerStationSummary,
  statisticsDailyUsageAndProfit,
  getSevenStatisticsAll,
  getBatteryRackList,
  getBatteryPackList,
  // getDeviceWorkProcessList,
  getBatteryWorkProcessDetail,
  statisticsSocAndWorkStatusByDay,
  getRecommendBatteryWorkProcess,
  // updateBatteryWorkProcess,
  getBatteryRack,
  deviceAlarmPage,
  clearAlarm,
  getDetail,
  updateStationInfo,
  historyPage,
  bindThirdAccount,
  getSubSupplierTree,
  statisticStationChargeAndProfitSummary,
  getWorkOrderPage,
  getInspectionDevicePage,
  getInspectionLogPage,
  addInspectionLog,
  getWorkOrderDetail,
  alarmTransfer,
  getOrgAndSubOrgStationNameList,
  getStaffByRole,
  manualCreate,
  updateWorkOrder,
};
