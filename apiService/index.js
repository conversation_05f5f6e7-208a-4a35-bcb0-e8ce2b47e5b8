import api from "@/api";
import { Get, Post, Delete,  } from "@/common/request";
function DDFreeAuthLogin(obj) {
  return Post({
    url: api.DDFreeAuthLogin,
    bodyParam: {
      ...obj,
      projectName: "MING_BATTERY_MONITOR",
    },
    config: {
      needToken: false,
    },
  });
}

function DDFreeLogin(params) {
  return Post({
    url: api.ddFreeLogin,
    bodyParam: params,
    config: {
      needToken: false,
    },
  });
}
function thirdUserLogin(params) {
  return Post({
    url: api.thirdUserLogin,
    bodyParam: params,
    config: {
      needToken: false,
    },
  });
}
function bindThirdAccount(params) {
  return Post({
    url: api.bindThirdAccount,
    bodyParam: params,
    config: {
      needToken: false,
    },
  });
}
function getBindThirdAccount() {
  return Get({
    url: api.getBindThirdAccount,
  });
}


function getAuthorizationPhone(params){
  return Post({
    url:api.authorizationAcquisitionPhone,
    bodyParam: params,
    config: {
      needToken: false,
    },
  })
}

function dingTalkUserAuthAcquirePhone({ authCode, thirdOrgId }) {
  return Post({
    url: api.dingTalkUserAuthAcquirePhone,
    bodyParam: {
      thirdOrgId,
      authCode,
      projectName: "MING_BATTERY_MONITOR",
    },
    config: {
      needToken: false,
    },
  });
}
function bindDeviceOrg(obj) {
  return Post({
    url: api.bindDeviceOrg,
    bodyParam: obj,
    config: {
      needToken: false,
    },
  });
}
function bindDevice(obj) {
  return Post({
    url: api.bindDevice,
    bodyParam: obj,
    // config: {
    //   needToken: false,
    // },
  });
}
// function getAllDeviceList() {
//   return Get({
//     url: api.getAllDeviceList,
//   });
// }
function getCustomerInfo() {
  return Get({
    url: api.getCustomerInfo,
  });
}
function queryAreaList(obj) {
  return Post({
    url: api.queryAreaList,
    bodyParam: obj,
    config: {
      needToken: false,
    },
  });
}

function getCurrentUserDetail(){
  return Post({
    url:api.getCurrentUserDetail
  })
}


export default {
  DDFreeAuthLogin,
  dingTalkUserAuthAcquirePhone,
  bindDeviceOrg,
  // getAllDeviceList,
  getCustomerInfo,
  bindDevice,
  queryAreaList,
  DDFreeLogin,
  thirdUserLogin,
  bindThirdAccount,
  getAuthorizationPhone,
  getCurrentUserDetail,
  getBindThirdAccount,
};
