import api from "@/api";
import { Get, Post } from '@/common/request'
function getVppStationInfo(stationId) {
  return Get({
      url: api.getVppStationInfo,
      pathParam: { stationId },
  })
}
function openVppStation(params) {
  return Post({
      url: api.openVppStation,
      bodyParam: params
  })
}
function getRealStrategyAndAiRecommendStrategy(stationId,runDt) {
  return Get({
      url: api.getRealStrategyAndAiRecommendStrategy,
      pathParam: {stationId,runDt},
  })
}

function getStationEmsBasicInfo(param) {
  return Get({
    url: api.getStationEmsBasicInfo,
    pathParam: {param},
  })
}
function getVppDemandRecords(params) {
  return Post({
    url: api.getVppDemandRecords,
    bodyParam: params
  })
}

function statisticsDailyChargeAndProfitDetail(params) {
  return Post({
    url: api.statisticsDailyChargeAndProfitDetail,
    bodyParam: params
  })
}
function statisticsStationDateChargeAndNetProfit(params) {
  return Post({
    url: api.statisticsStationDateChargeAndNetProfit,
    bodyParam: params
  })
}

function openVppAiStrategy(params) {
  return Post({
    url: api.openVppAiStrategy,
    bodyParam: params
  })
}
function openVppDemand(params) {
  return Post({
    url: api.openVppDemand,
    bodyParam: params
  })
}
function getConfigEarningParams(param) {
  return Get({
    url: api.getConfigEarningParams,
    pathParam: {param},
  })
}
function configEarningParams(params) {
  return Post({
    url: api.configEarningParams,
    bodyParam: params
  })
}
function getVppDemandTotalSettlement(param) {
  return Get({
    url: api.getVppDemandTotalSettlement,
    pathParam: {param},
  })
}


export default {
  getVppStationInfo,
  openVppStation,
  getRealStrategyAndAiRecommendStrategy,
  getStationEmsBasicInfo,
  getVppDemandRecords,
  statisticsDailyChargeAndProfitDetail,
  statisticsStationDateChargeAndNetProfit,
  openVppAiStrategy,
  openVppDemand,
  getConfigEarningParams,
  configEarningParams,
  getVppDemandTotalSettlement,
}