/* Width Utilities */
.w-0 {
  width: 0;
}

.w-1 {
  width: 4rpx;
}

.w-2 {
  width: 8rpx;
}

.w-3 {
  width: 12rpx;
}

.w-4 {
  width: 16rpx;
}

.w-5 {
  width: 20rpx;
}

.w-6 {
  width: 24rpx;
}

.w-7 {
  width: 28rpx;
}

.w-8 {
  width: 32rpx;
}

.w-9 {
  width: 36rpx;
}

.w-10 {
  width: 40rpx;
}

.w-full {
  width: 100%;
}

/* Height Utilities */
.h-0 {
  height: 0;
}

.h-1 {
  height: 4rpx;
}

.h-2 {
  height: 8rpx;
}

.h-3 {
  height: 12rpx;
}

.h-4 {
  height: 16rpx;
}

.h-5 {
  height: 20rpx;
}

.h-6 {
  height: 24rpx;
}

.h-7 {
  height: 28rpx;
}

.h-8 {
  height: 32rpx;
}

.h-9 {
  height: 36rpx;
}

.h-10 {
  height: 40rpx;
}

.h-full {
  height: 100%;
}

/* Line Height Utilities */
.leading-0 {
  line-height: 0;
}

.leading-1 {
  line-height: 4rpx;
}

.leading-2 {
  line-height: 8rpx;
}

.leading-3 {
  line-height: 12rpx;
}

.leading-4 {
  line-height: 16rpx;
}

.leading-5 {
  line-height: 20rpx;
}

.leading-6 {
  line-height: 24rpx;
}

.leading-7 {
  line-height: 28rpx;
}

.leading-8 {
  line-height: 32rpx;
}

.leading-9 {
  line-height: 36rpx;
}

.leading-10 {
  line-height: 40rpx;
}

.leading-none {
  line-height: 1;
}


/* Margin Utilities */
.m-0 {
  margin: 0;
}

.m-1 {
  margin: 4rpx;
}

.m-2 {
  margin: 8rpx;
}

.m-3 {
  margin: 12rpx;
}

.m-4 {
  margin: 16rpx;
}

.m-5 {
  margin: 20rpx;
}

.m-6 {
  margin: 24rpx;
}

.m-7 {
  margin: 28rpx;
}

.m-8 {
  margin: 32rpx;
}

.m-9 {
  margin: 36rpx;
}

.m-10 {
  margin: 40rpx;
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: 4rpx;
}

.mt-2 {
  margin-top: 8rpx;
}

.mt-3 {
  margin-top: 12rpx;
}

.mt-4 {
  margin-top: 16rpx;
}

.mt-5 {
  margin-top: 20rpx;
}

.mt-6 {
  margin-top: 24rpx;
}

.mt-7 {
  margin-top: 28rpx;
}

.mt-8 {
  margin-top: 32rpx;
}

.mt-9 {
  margin-top: 36rpx;
}

.mt-10 {
  margin-top: 40rpx;
}

.mr-0 {
  margin-right: 0;
}

.mr-1 {
  margin-right: 4rpx;
}

.mr-2 {
  margin-right: 8rpx;
}

.mr-3 {
  margin-right: 12rpx;
}

.mr-4 {
  margin-right: 16rpx;
}

.mr-5 {
  margin-right: 20rpx;
}

.mr-6 {
  margin-right: 24rpx;
}

.mr-7 {
  margin-right: 28rpx;
}

.mr-8 {
  margin-right: 32rpx;
}

.mr-9 {
  margin-right: 36rpx;
}

.mr-10 {
  margin-right: 40rpx;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: 4rpx;
}

.mb-2 {
  margin-bottom: 8rpx;
}

.mb-3 {
  margin-bottom: 12rpx;
}

.mb-4 {
  margin-bottom: 16rpx;
}

.mb-5 {
  margin-bottom: 20rpx;
}

.mb-6 {
  margin-bottom: 24rpx;
}

.mb-7 {
  margin-bottom: 28rpx;
}

.mb-8 {
  margin-bottom: 32rpx;
}

.mb-9 {
  margin-bottom: 36rpx;
}

.mb-10 {
  margin-bottom: 40rpx;
}

.ml-0 {
  margin-left: 0;
}

.ml-1 {
  margin-left: 4rpx;
}

.ml-2 {
  margin-left: 8rpx;
}

.ml-3 {
  margin-left: 12rpx;
}

.ml-4 {
  margin-left: 16rpx;
}

.ml-5 {
  margin-left: 20rpx;
}

.ml-6 {
  margin-left: 24rpx;
}

.ml-7 {
  margin-left: 28rpx;
}

.ml-8 {
  margin-left: 32rpx;
}

.ml-9 {
  margin-left: 36rpx;
}

.ml-10 {
  margin-left: 40rpx;
}

.mx-0 {
  margin-left: 0;
  margin-right: 0;
}

.mx-1 {
  margin-left: 4rpx;
  margin-right: 4rpx;
}

.mx-2 {
  margin-left: 8rpx;
  margin-right: 8rpx;
}

.mx-3 {
  margin-left: 12rpx;
  margin-right: 12rpx;
}

.mx-4 {
  margin-left: 16rpx;
  margin-right: 16rpx;
}

.mx-5 {
  margin-left: 20rpx;
  margin-right: 20rpx;
}

.mx-6 {
  margin-left: 24rpx;
  margin-right: 24rpx;
}

.mx-7 {
  margin-left: 28rpx;
  margin-right: 28rpx;
}

.mx-8 {
  margin-left: 32rpx;
  margin-right: 32rpx;
}

.mx-9 {
  margin-left: 36rpx;
  margin-right: 36rpx;
}

.mx-10 {
  margin-left: 40rpx;
  margin-right: 40rpx;
}

.my-0 {
  margin-top: 0;
  margin-bottom: 0;
}

.my-1 {
  margin-top: 4rpx;
  margin-bottom: 4rpx;
}

.my-2 {
  margin-top: 8rpx;
  margin-bottom: 8rpx;
}

.my-3 {
  margin-top: 12rpx;
  margin-bottom: 12rpx;
}

.my-4 {
  margin-top: 16rpx;
  margin-bottom: 16rpx;
}

.my-5 {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}

.my-6 {
  margin-top: 24rpx;
  margin-bottom: 24rpx;
}

.my-7 {
  margin-top: 28rpx;
  margin-bottom: 28rpx;
}

.my-8 {
  margin-top: 32rpx;
  margin-bottom: 32rpx;
}

.my-9 {
  margin-top: 36rpx;
  margin-bottom: 36rpx;
}

.my-10 {
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}

/* 其他方向：mr-, mb-, ml-, mx-, my- 同理省略 */

/* Padding Utilities */
.p-0 {
  padding: 0;
}

.p-1 {
  padding: 4rpx;
}

.p-2 {
  padding: 8rpx;
}

.p-3 {
  padding: 12rpx;
}

.p-4 {
  padding: 16rpx;
}

.p-5 {
  padding: 20rpx;
}

.p-6 {
  padding: 24rpx;
}

.p-7 {
  padding: 28rpx;
}

.p-8 {
  padding: 32rpx;
}

.p-9 {
  padding: 36rpx;
}

.p-10 {
  padding: 40rpx;
}

.pt-0 {
  padding-top: 0;
}

.pt-1 {
  padding-top: 4rpx;
}

.pt-2 {
  padding-top: 8rpx;
}

.pt-3 {
  padding-top: 12rpx;
}

.pt-4 {
  padding-top: 16rpx;
}

.pt-5 {
  padding-top: 20rpx;
}

.pt-6 {
  padding-top: 24rpx;
}

.pt-7 {
  padding-top: 28rpx;
}

.pt-8 {
  padding-top: 32rpx;
}

.pt-9 {
  padding-top: 36rpx;
}

.pt-10 {
  padding-top: 40rpx;
}

.pr-0 {
  padding-right: 0;
}

.pr-1 {
  padding-right: 4rpx;
}

.pr-2 {
  padding-right: 8rpx;
}

.pr-3 {
  padding-right: 12rpx;
}

.pr-4 {
  padding-right: 16rpx;
}

.pr-5 {
  padding-right: 20rpx;
}

.pr-6 {
  padding-right: 24rpx;
}

.pr-7 {
  padding-right: 28rpx;
}

.pr-8 {
  padding-right: 32rpx;
}

.pr-9 {
  padding-right: 36rpx;
}

.pr-10 {
  padding-right: 40rpx;
}

.pb-0 {
  padding-bottom: 0;
}

.pb-1 {
  padding-bottom: 4rpx;
}

.pb-2 {
  padding-bottom: 8rpx;
}

.pb-3 {
  padding-bottom: 12rpx;
}

.pb-4 {
  padding-bottom: 16rpx;
}

.pb-5 {
  padding-bottom: 20rpx;
}

.pb-6 {
  padding-bottom: 24rpx;
}

.pb-7 {
  padding-bottom: 28rpx;
}

.pb-8 {
  padding-bottom: 32rpx;
}

.pb-9 {
  padding-bottom: 36rpx;
}

.pb-10 {
  padding-bottom: 40rpx;
}

.pl-0 {
  padding-left: 0;
}

.pl-1 {
  padding-left: 4rpx;
}

.pl-2 {
  padding-left: 8rpx;
}

.pl-3 {
  padding-left: 12rpx;
}

.pl-4 {
  padding-left: 16rpx;
}

.pl-5 {
  padding-left: 20rpx;
}

.pl-6 {
  padding-left: 24rpx;
}

.pl-7 {
  padding-left: 28rpx;
}

.pl-8 {
  padding-left: 32rpx;
}

.pl-9 {
  padding-left: 36rpx;
}

.pl-10 {
  padding-left: 40rpx;
}

.px-0 {
  padding-left: 0;
  padding-right: 0;
}

.px-1 {
  padding-left: 4rpx;
  padding-right: 4rpx;
}

.px-2 {
  padding-left: 8rpx;
  padding-right: 8rpx;
}

.px-3 {
  padding-left: 12rpx;
  padding-right: 12rpx;
}

.px-4 {
  padding-left: 16rpx;
  padding-right: 16rpx;
}

.px-5 {
  padding-left: 20rpx;
  padding-right: 20rpx;
}

.px-6 {
  padding-left: 24rpx;
  padding-right: 24rpx;
}

.px-7 {
  padding-left: 28rpx;
  padding-right: 28rpx;
}

.px-8 {
  padding-left: 32rpx;
  padding-right: 32rpx;
}

.px-9 {
  padding-left: 36rpx;
  padding-right: 36rpx;
}

.px-10 {
  padding-left: 40rpx;
  padding-right: 40rpx;
}

.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.py-1 {
  padding-top: 4rpx;
  padding-bottom: 4rpx;
}

.py-2 {
  padding-top: 8rpx;
  padding-bottom: 8rpx;
}

.py-3 {
  padding-top: 12rpx;
  padding-bottom: 12rpx;
}

.py-4 {
  padding-top: 16rpx;
  padding-bottom: 16rpx;
}

.py-5 {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}

.py-6 {
  padding-top: 24rpx;
  padding-bottom: 24rpx;
}

.py-7 {
  padding-top: 28rpx;
  padding-bottom: 28rpx;
}

.py-8 {
  padding-top: 32rpx;
  padding-bottom: 32rpx;
}

.py-9 {
  padding-top: 36rpx;
  padding-bottom: 36rpx;
}

.py-10 {
  padding-top: 40rpx;
  padding-bottom: 40rpx;
}

/* Flex Utilities */
.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.items-end {
  align-items: flex-end;
}

.justify-start {
  justify-content: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

/* Position Utilities */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

.top-0 {
  top: 0;
}

.top-1 {
  top: 4rpx;
}

.top-2 {
  top: 8rpx;
}

.top-3 {
  top: 12rpx;
}

.top-4 {
  top: 16rpx;
}

.top-5 {
  top: 20rpx;
}

.top-6 {
  top: 24rpx;
}

.top-7 {
  top: 28rpx;
}

.top-8 {
  top: 32rpx;
}

.top-9 {
  top: 36rpx;
}

.top-10 {
  top: 40rpx;
}

.right-0 {
  right: 0;
}

.right-1 {
  right: 4rpx;
}

.right-2 {
  right: 8rpx;
}

.right-3 {
  right: 12rpx;
}

.right-4 {
  right: 16rpx;
}

.right-5 {
  right: 20rpx;
}

.right-6 {
  right: 24rpx;
}

.right-7 {
  right: 28rpx;
}

.right-8 {
  right: 32rpx;
}

.right-9 {
  right: 36rpx;
}

.right-10 {
  right: 40rpx;
}

.bottom-0 {
  bottom: 0;
}

.bottom-1 {
  bottom: 4rpx;
}

.bottom-2 {
  bottom: 8rpx;
}

.bottom-3 {
  bottom: 12rpx;
}

.bottom-4 {
  bottom: 16rpx;
}

.bottom-5 {
  bottom: 20rpx;
}

.bottom-6 {
  bottom: 24rpx;
}

.bottom-7 {
  bottom: 28rpx;
}

.bottom-8 {
  bottom: 32rpx;
}

.bottom-9 {
  bottom: 36rpx;
}

.bottom-10 {
  bottom: 40rpx;
}

.left-0 {
  left: 0;
}

.left-1 {
  left: 4rpx;
}

.left-2 {
  left: 8rpx;
}

.left-3 {
  left: 12rpx;
}

.left-4 {
  left: 16rpx;
}

.left-5 {
  left: 20rpx;
}

.left-6 {
  left: 24rpx;
}

.left-7 {
  left: 28rpx;
}

.left-8 {
  left: 32rpx;
}

.left-9 {
  left: 36rpx;
}

.left-10 {
  left: 40rpx;
}

/* Text Alignment Utilities */
.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}