page {
  color: $uni-main-color;
  box-sizing: border-box;
  font-size: 28rpx;
  //#ifdef MP-DINGTALK
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: calc(-30rpx + env(safe-area-inset-bottom));
  //#endif
}

text {
  vertical-align: middle;
  line-height: 40rpx;
}

view {
  box-sizing: border-box;
  color: $uni-main-color;
  // line-height: 40rpx;
}

.bg-white {
  background-color: #fff;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.w-0 {
  width: 0;
}

p {
  margin-bottom: 0px;
}

h1 {
  font-size: 40rpx;
  font-weight: 500;
  line-height: 56rpx;
}

.hr {
  border-bottom: none;
  border-left: none;
  border-right: none;
  border-top: 1px solid #e6e6e8;
  margin: 0;
}

.button-hover[type="primary"] {
  background-color: $uni-primary;
}

button.large {
  border-radius: 16rpx;
  height: 92rpx;
  line-height: 92rpx;
}

button[plain] {
  border: 2rpx solid #e6e6e8;
}

button.primary {
  background-color: $uni-primary;
  box-sizing: border-box;
  color: #fff;
}

button[type="primary"][plain] {
  background-color: transparent;
  border: 1px solid $uni-primary;
  color: $uni-primary;
}

button[disabled].primary {
  background-color: rgba($uni-primary, 0.5);
}

.primary-color {
  color: $uni-primary;
}

button {
  height: 64rpx;
  line-height: 64rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
  margin: 0;
  padding: 0 24rpx;
  text-align: center;
}

.cursor {
  cursor: pointer;
}

.overflow {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}

.mutiLineOverflow {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.icon-xs {
  width: 24rpx;
  height: 24rpx;
  vertical-align: middle;
}

.icon-sm {
  width: 28rpx;
  height: 28rpx;
}

.icon-md {
  width: 32rpx;
  height: 32rpx;
}

.icon-xl {
  width: 36rpx;
  height: 36rpx;
}

.icon-2xl {
  width: 40rpx;
  height: 40rpx;

}

.align-middle {
  vertical-align: middle;
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
}

.success {
  color: #1ECC99;
}

.error {
  color: #FD0B0B;
}

.extra-color {
  color: $uni-extra-color
}