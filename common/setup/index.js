import {
  ref,
  onBeforeUnmount,
  onMounted,
  reactive,
  getCurrentInstance,
} from "vue";
import { onReachBottom } from "@dcloudio/uni-app";

//发送验证码按钮禁用
export function useDisableTime() {
  const disableTime = ref(0);
  let disableTimeInterval = null;
  const setDisableTime = () => {
    if (disableTimeInterval) {
      clearInterval(disableTimeInterval);
    }
    disableTime.value = 60;
    disableTimeInterval = setInterval(() => {
      disableTime.value--;
      if (disableTime.value == 0) {
        clearInterval(disableTimeInterval);
      }
    }, 1000);
  };
  onBeforeUnmount(() => {
    if (disableTimeInterval) {
      clearInterval(disableTimeInterval);
    }
    disableTime.value = 0;
  });
  return {
    disableTime,
    setDisableTime,
  };
}
//分页下拉加载
export function useLoadMore(pageSize, getList) {

  const page = reactive({
    current: 1, //当前
    size: pageSize, //每页条数
    // totalPages: 0, //总页数
    // sortType: "create_time",
    // sortOrder: "desc",
  });
  const setTotalPage = (total) => {
    page.totalPages = total;
  };
  const loadMoreStatus = ref("nomore");
  const handleMore = async () => {
    if (page.current < page.totalPages) {
      loadMoreStatus.value = "loading";
      page.current++;
      await getList();
      loadMoreStatus.value = page.current < page.totalPages ? "more" : "nomore";
    }
  };
  onReachBottom(() => {
    handleMore && handleMore();
  });
  return {
    loadMoreStatus,
    setTotalPage,
    page,
  };
};
import { chargeState, chargeStateP } from "../constant";
export const getState = (status, type) => {
  if (type == 'power') {
    const item = chargeStateP.find((s) => s.value == status) || {};
    return item;
  } else {
    const item = chargeState.find((s) => s.value == status) || {};
    return item;
  }
};