const priceName = {
  highestPeakPrice: "尖峰电价",
  peakPrice: "峰时电价",
  normalPrice: "平时电价",
  valleyPrice: "谷时电价",
  lowestValleyPrice: "深谷电价",
};
function o(t, e) {
  (null == e || e > t.length) && (e = t.length);
  for (var n = 0, r = new Array(e); n < e; n++) r[n] = t[n];
  return r;
}
function c(t) {
  return (
    (function (t) {
      if (Array.isArray(t)) return o(t);
    })(t) ||
    (function (t) {
      if (
        ("undefined" != typeof Symbol && null != t[Symbol.iterator]) ||
        null != t["@@iterator"]
      )
        return Array.from(t);
    })(t) ||
    a(t) ||
    (function () {
      throw new TypeError(
        "Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."
      );
    })()
  );
}
function showPrice(t) {
  return "number" == typeof t && t ? t.toFixed(2) : "--";
}
function getPricePercentage(t, prices) {
  let v = 100,
    y = Math.max.apply(Math, c(Object.values(prices)));
  return t ? (t / y) * v + "px" : 0;
}
function getMaxPrice(t) {
  if (t.length > 0) {
    var e,
      n = t[0].price,
      r = (function (t, e) {
        var n =
          ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
          t["@@iterator"];
        if (!n) {
          if (
            Array.isArray(t) ||
            (n = (function (t, e) {
              if (t) {
                if ("string" == typeof t) return T(t, e);
                var n = Object.prototype.toString.call(t).slice(8, -1);
                return (
                  "Object" === n && t.constructor && (n = t.constructor.name),
                  "Map" === n || "Set" === n
                    ? Array.from(t)
                    : "Arguments" === n ||
                      /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
                    ? T(t, e)
                    : void 0
                );
              }
            })(t)) ||
            (e && t && "number" == typeof t.length)
          ) {
            n && (t = n);
            var r = 0,
              i = function () {};
            return {
              s: i,
              n: function () {
                return r >= t.length
                  ? {
                      done: !0,
                    }
                  : {
                      done: !1,
                      value: t[r++],
                    };
              },
              e: function (t) {
                throw t;
              },
              f: i,
            };
          }
          throw new TypeError(
            "Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."
          );
        }
        var o,
          a = !0,
          c = !1;
        return {
          s: function () {
            n = n.call(t);
          },
          n: function () {
            var t = n.next();
            return (a = t.done), t;
          },
          e: function (t) {
            (c = !0), (o = t);
          },
          f: function () {
            try {
              a || null == n.return || n.return();
            } finally {
              if (c) throw o;
            }
          },
        };
      })(t);
    try {
      for (r.s(); !(e = r.n()).done; ) {
        var i = e.value;
        i.price > n && (n = i.price);
      }
    } catch (t) {
      r.e(t);
    } finally {
      r.f();
    }
    return n;
  }
  return 1;
}
function formatData(t, e) {
  var n = ["尖", "峰", "平", "谷", "深谷"],
    r = {
      充电: 1,
      放电: 2,
      静置: 0,
    },
    i = [
      "highestPeakPrice",
      "peakPrice",
      "normalPrice",
      "valleyPrice",
      "lowestValleyPrice",
    ],
    prices = [],
    areas = [];
  t.workProcessList.map(function (t) {
    var c = i[t.segmentType - 1];
    prices.push({
      startTime: t.startHour,
      endTime: t.endHour,
      priceAmount: t.value,
      type: n[t.segmentType - 1],
      price: e[c],
      segmentType: t.segmentType,
    });
    areas.push({
      type: t.batteryWorkStatus,
      typeDescription: r[t.batteryWorkStatus],
      startTime: t.startHour,
      endTime: t.endHour,
    });
  });
  return {
    prices,
    areas,
  };
}
function drawDashLine(t, x1, y1, x2, y2) {
  var dashLength =
      arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : 3,
    dx = x2 - x1,
    dy = y2 - y1,
    //虚线数量
    numDashes = Math.floor(Math.sqrt(dx * dx + dy * dy) / dashLength),
    u = dx / numDashes,
    f = dy / numDashes;
  t.beginPath();
  for (var i = 0; i < numDashes; i++) {
    i % 2 == 0 ? t.moveTo(x1, y1) : t.lineTo(x1, y1);
    x1 += u;
    y1 += f;
  }
  t.lineTo(x2, y2);
}
class DrawCanvas {
  constructor(context) {
    // var n = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : [];
    // debugger;
    // s(this, t);
    let { devicePixelRatio, windowWidth, screenWidth } =
      uni.getSystemInfoSync();
    const screenRatio = screenWidth / 375;
    this.dpr = devicePixelRatio;
    // this.canvas = e;
    this.context = context;
    this.timeSegment = 24;
    this.padding = 24 * this.dpr;
    this.tickStyle = "#000";
    this.axisStyle = "#999";
    this.prices = [];
    this.areas = [];
    this.maxPrice = getMaxPrice(this.prices);
    this.showAxisPointer = !0;
    this.mouseX = 0;
    this.mouseY = 0;
    // this.setup();
    this.width = windowWidth * this.dpr;
    this.height = 215 * this.dpr;
    this.fontSize = 10 * this.dpr;
    this.lineWidth = 1 * this.dpr;
  }
  // get width() {
  //   return this.canvas.width;
  // }
  // set width(value) {
  //   this.canvas.width = newValue;
  // }
  // get height() {
  //   return this.canvas.height;
  // }
  // set height(newValue) {
  //   this.canvas.height = newValue;
  // }
  get contentBox() {
    return {
      x: this.padding,
      y: this.padding,
      width: this.width - 2 * this.padding,
      height: this.height - 2 * this.padding,
    };
  }
  // setup() {
  //   var t = this;
  //   this.canvas.addEventListener("mousemove", function (e) {
  //     var n = e.offsetX,
  //       r = e.offsetY;
  //     t.mouseX = n;
  //     t.mouseY = r;
  //     t.clear();
  //     t.paint();
  //     t.showAxisPointer && P(t.contentBox, n, r) && t.drawAxisPointer(n, r);
  //   });
  // }
  paint() {
    this.drawXAxis();
    this.drawYAxis();
    this.drawArea();
    this.drawPriceLine();
    this.context.draw();
  }
  drawXAxis() {
    var t = this.context,
      //x轴间隔
      gap = (this.width - 2 * this.padding) / this.timeSegment;
    t.save();
    //移动坐标系原点到左下角
    t.translate(0, this.height - this.padding - 5 * this.dpr);
    t.setLineWidth(this.lineWidth);
    t.beginPath();
    t.setStrokeStyle(this.axisStyle);
    //将绘图游标移动到指定的坐标位置，而不进行绘制。
    t.moveTo(this.padding, 5 * this.dpr);
    //线的终点位置（这里的5，因为原点已经移到左下角了）
    t.lineTo(this.width - this.padding, 5 * this.dpr);
    t.stroke();
    t.setFillStyle(this.tickStyle);
    //以上是从左到右画X轴
    for (var n = 0; n <= this.timeSegment; n++) {
      //开始画x轴上的刻度小竖线
      var startX = gap * n + this.padding, //起始x轴位置
        startY = 0, //起始y轴位置
        endY = startY + 5 * this.dpr; //结束y轴位置，刻度高度为5
      //如果X轴刻度到12或者24时，刻度高度稍微高一点（y轴往上就是负数）
      0 !== n && n % 12 == 0 && (startY -= 2 * this.dpr);
      t.beginPath();
      t.moveTo(startX, startY);
      t.lineTo(startX, endY);
      t.stroke();
      //开始画刻度下方的数字
      t.beginPath();
      t.setFontSize(this.fontSize);
      t.setTextAlign("center");
      t.setFillStyle("rgba(34, 34, 34, 0.4)");
      t.fillText(n, startX, endY + 20 * this.dpr); //+20表示在y轴的下方
      t.closePath();
    }
    t.restore();
  }
  drawYAxis() {
    var t = this.context,
      gap = (this.height - 2 * this.padding) / 10, //把Y轴刻度分成10等分
      priceGap = this.maxPrice / 10; //将最大价格10等分
    t.save();
    t.beginPath();
    t.setStrokeStyle(this.axisStyle);
    //绘图游标移动到左下角起始位置
    t.moveTo(this.padding, this.padding);
    //从下往上绘制Y轴竖线
    t.lineTo(this.padding, this.height - this.padding);
    t.stroke();
    t.setFillStyle(this.tickStyle);
    for (var i = 0; i < 10; i++) {
      var startX = this.padding,
        startY = i * gap + this.padding,
        endX = startX + 5 * this.dpr, //刻度宽度为5
        //计算Y轴的刻度值（将最大价格10等分）
        text = (this.maxPrice - priceGap * i).toFixed(2);
      //每5个刻度，刻度长度大一点
      i % 5 == 0 && (endX += 2 * this.dpr);
      t.beginPath();
      t.moveTo(startX, startY);
      t.lineTo(endX, startY);
      t.stroke();
      t.setFontSize(this.fontSize);
      t.setTextAlign("center");
      t.setFillStyle("rgba(34, 34, 34, 0.4)");
      t.textBaseline = "middle";
      t.fillText(text, startX - 14 * this.dpr, startY);
    }
    t.restore();
  }
  drawPriceLine() {
    var t = this.context;
    t.save();
    t.setFillStyle(this.tickStyle);
    t.setStrokeStyle("#222");
    // debugger;
    for (var i = 0; i < this.prices.length; i++) {
      var item = this.prices[i],
        text = item.price.toFixed(2) + " (" + item.type + ")",
        startTimePx = this.timeToPixel(item.startTime),
        endTimePx = this.timeToPixel(item.endTime),
        textX = startTimePx + (endTimePx - startTimePx) / 2,
        pricePx = this.priceToPixel(item.price),
        textY = pricePx - 4 * this.dpr;
      if (this._last_x && this._last_y) {
        //绘制和前面的价格相连的虚线
        t.setLineWidth(this.lineWidth);
        t.setStrokeStyle("#999");
        drawDashLine(t, this._last_x, this._last_y, startTimePx, pricePx);
        t.stroke();
      }
      t.setStrokeStyle("#00AEEF");
      t.setLineWidth(this.lineWidth);
      t.beginPath();
      t.moveTo(startTimePx, pricePx);
      t.lineTo(endTimePx, pricePx);
      t.stroke();
      t.setFillStyle("#00AEEF");
      t.setFontSize(12 * this.dpr);
      t.setTextAlign("center");
      t.textBaseline = "middle";
      //处理特殊情况：如果跟前面的价格一样的话只显示一条即可
      let last = i - 1 >= 0 ? this.prices[i - 1] : null;
      if (!(last && last.type == item.type && last.price == item.price)) {
        t.fillText(text, textX, textY);
      }
      this._last_x = endTimePx;
      this._last_y = pricePx;
    }
    t.restore();
  }
  drawArea() {
    var t = this.context,
      colors = [
        "rgba(0,0,0,0)",
        "rgba(111, 190, 206, 0.5)",
        "rgba(253, 117, 11, 0.2)",
      ];
    t.save();
    this.areas.forEach((item) => {
      var r = this.areaBox(item);
      t.setFillStyle(colors[item.type]);
      t.fillRect(r.x, r.y, r.width, r.height);
    });
    t.restore();
  }
  drawAxisPointer(t, e) {
    var n =
        arguments.length > 2 && void 0 !== arguments[2]
          ? arguments[2]
          : "#FF00F0",
      r = this.context;
    r.save();
    r.setLineWidth(this.lineWidth);
    r.setStrokeStyle(n);
    r.setFillStyle(n);
    r.beginPath();
    r.arc(t, e, 3, 0, 2 * Math.PI, !1);
    r.fill();
    var i = this.padding,
      o = this.width - this.padding;
    drawDashLine(r, i, e, o, e, 5 * this.dpr);
    r.stroke();
    var a = this.padding,
      c = this.height - this.padding;
    drawDashLine(r, t, a, t, c, 5 * this.dpr);
    r.stroke();
    r.setFillStyle("#00AEEF");
    r.fillRect(t - 140, e - 50, 280, 40);
    r.beginPath();
    r.moveTo(t, e - 3);
    r.lineTo(t - 8, e - 10);
    r.lineTo(t + 8, e - 10);
    r.fill();
    var s = this.priceFromPixel(e),
      l = this.timeFromPixel(t),
      u = this.chargeStatusFromPixel(t, e),
      f = "时间:".concat(l, " 电价:").concat(s, " ").concat(u);
    t.setFontSize(14 * this.dpr);
    r.setTextAlign("center");
    r.setFillStyle("#fff");
    r.fillText(f, t + 5, e - 25, 260);
    r.restore();
  }
  getPercentageX(t) {
    return (t - this.padding) / this.contentBox.width;
  }
  areaBox(t) {
    var x = this.timeToPixel(t.startTime);
    return {
      x,
      y: this.padding,
      width: this.timeToPixel(t.endTime) - x,
      height: this.height - 2 * this.padding,
    };
  }
  priceFromPixel(t) {
    var e = (this.height - t - this.padding) / this.contentBox.height;
    return (this.maxPrice * e).toFixed(2);
  }
  timeFromPixel(t) {
    var e = 1440 * this.getPercentageX(t),
      n = Math.floor(e / 60),
      r = Math.floor(e % 60),
      i = function (t) {
        return String(t).padStart(2, "0");
      };
    return i(n) + ":" + i(r);
  }
  chargeStatusFromPixel(t, e) {
    var n,
      r = ["充电↓", "放电↑", "静置"],
      i = (function (t, e) {
        var n =
          ("undefined" != typeof Symbol && t[Symbol.iterator]) ||
          t["@@iterator"];
        if (!n) {
          if (
            Array.isArray(t) ||
            (n = (function (t, e) {
              if (t) {
                if ("string" == typeof t) return R(t, e);
                var n = Object.prototype.toString.call(t).slice(8, -1);
                return (
                  "Object" === n && t.constructor && (n = t.constructor.name),
                  "Map" === n || "Set" === n
                    ? Array.from(t)
                    : "Arguments" === n ||
                      /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)
                    ? R(t, e)
                    : void 0
                );
              }
            })(t)) ||
            (e && t && "number" == typeof t.length)
          ) {
            n && (t = n);
            var r = 0,
              i = function () {};
            return {
              s: i,
              n: function () {
                return r >= t.length
                  ? {
                      done: !0,
                    }
                  : {
                      done: !1,
                      value: t[r++],
                    };
              },
              e: function (t) {
                throw t;
              },
              f: i,
            };
          }
          throw new TypeError(
            "Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."
          );
        }
        var o,
          a = !0,
          c = !1;
        return {
          s: function () {
            n = n.call(t);
          },
          n: function () {
            var t = n.next();
            return (a = t.done), t;
          },
          e: function (t) {
            (c = !0), (o = t);
          },
          f: function () {
            try {
              a || null == n.return || n.return();
            } finally {
              if (c) throw o;
            }
          },
        };
      })(this.areas);
    try {
      for (i.s(); !(n = i.n()).done; ) {
        var o = n.value;
        if (P(this.areaBox(o), t, e)) return r[o.type] || "";
      }
    } catch (t) {
      i.e(t);
    } finally {
      i.f();
    }
    return "";
  }
  priceToPixel(t) {
    var e = (this.height - 2 * this.padding) / this.maxPrice;
    return this.height - this.padding - t * e;
  }
  timeToPixel(hour) {
    // 原本值是"08:00"
    // var e = t.split(/[:：]/),
    //   n = (60 * e[0] + 1 * e[1]) / 1440;
    //现在值是8
    var n = (60 * hour) / 1440; //1440=60*24一天的分钟数
    return this.padding + (this.width - 2 * this.padding) * n;
  }
  clear() {
    this._last_x = null;
    this._last_y = null;
    this.context.clearRect(0, 0, this.width, this.height);
  }
  update(t) {
    this.prices = t.prices || [];
    this.areas = t.areas || [];
    this.maxPrice = getMaxPrice(t.prices);
  }
}
export { DrawCanvas, formatData, priceName, showPrice, getPricePercentage };
