// import { $http, RequestUse, ResponseUse } from "./base";
import { responseIntercept, createResponseInterceptError } from "./errorHandle";
import { Post, Delete, Put, Get } from "./methods";
// import { message } from "ant-design-vue";
import { errorMsg } from "@/common/constant";
// import service from "@/apiService";
// import router from "@/router";

let errorHandler = (status, errorMessage) => {
  errorMessage = errorMsg[status] || errorMessage;
  if (status === 401) {
    // 清除缓存
    uni.removeStorageSync("accessToken");
    // 登录
    uni.showLoading({
      title: "登录已失效，正在重新登录",
    });
    console.log('401，请求接口中',)
    uni.redirectTo({
      url: '/packageDevice/login'
    })
    uni.hideLoading();
  } else {
    if (status === 100006) {
      uni.reLaunch({
        url: "/pages/noright",
      });
      return;
    } else {
      uni.hideLoading();
      if (status == 10603) {
        uni.showToast({
          icon: "none",
          title: errorMessage,
          duration: 3000,
        });
      } else {
        uni.showToast({
          icon: "error",
          title: errorMessage,
          duration: 3000,
        });
      }
    }
  }
};
uni.addInterceptor("request", {
  invoke(args) {
    // request 触发前
    if (args.needToken != false) {
      const token = uni.getStorageSync("accessToken");
      if (token) {
        args.header.Authorization = `Bearer ${token}`;
        // args.header.Authorization = `Bearer 8b61b14a7d5e468cb93f9805d17257bd`
      }
    }
    let id = undefined
    //#ifdef MP-DINGTALK
    id = dd.corpId
    //#endif
    if (args.header) {
      args.header.platform = "weChat";
      args.header.corpId = id;
      args.header.projectName = "MING_BATTERY_MONITOR";
    }
  },
  async success(args) {
    // 请求成功后，通过判断code处理逻辑错误
    let res = await responseIntercept(errorHandler)(args);
    return res;
  },
  fail(err) {
    if (err.statusCode == 401) {
      //钉钉的401会走到这里，特殊处理下
      err.response = {};
      err.response.status = 401;
    }
    //请求失败
    createResponseInterceptError(errorHandler)(err);
  },
  complete(res) {
  },
});

// ResponseUse(
//   responseIntercept(errorHandler),
//   createResponseInterceptError(errorHandler)
// );

export { Post, Delete, Put, Get };
