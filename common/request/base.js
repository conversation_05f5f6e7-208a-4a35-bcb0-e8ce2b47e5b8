// import axios from 'axios'

let $http = axios.create({
    headers: {
        'X-Requested-With': 'XMLHttpRequest',
        // 'x-requested-with': undefined
    },
    timeout: 10000
});

function RequestUse(request, requestError) {
    $http.interceptors.request.use(request, requestError);
}

function ResponseUse(response, responseError) {
    $http.interceptors.response.use(response, responseError);
}

export {
    $http,
    RequestUse,
    ResponseUse
};