// import { $http } from "./base.js";

const urlReplace = function (url, pathParam) {
  let newUrl = url;
  let urls = url.match(/\{[^\}]+\}/g);
  if (pathParam) {
    for (var i in pathParam) {
      pathParam[i] = encodeURIComponent(pathParam[i]);
      for (var j = 0; j < urls.length; j++) {
        if (urls[j].slice(1, -1) === i) {
          newUrl =
            newUrl.split(urls[j])[0] + pathParam[i] + newUrl.split(urls[j])[1];
        }
      }
    }
  }
  return newUrl;
};

function fetch1({ method, url, data, pathParam, config = null }) {
  let fullUrl = urlReplace(url, pathParam);
  let p = new Promise((resolve, reject) => {
    uni.request({
      url: fullUrl,
      method,
      data,
      header: {},
      ...config,
      success: (res) => {
        resolve(res);
      },
      fail: (error) => {
        reject(error);
      },
    });
  });
  return p;
}

function Get({ url, pathParam = null, config = null }) {
  return fetch1({
    method: "GET",
    url,
    pathParam,
    config,
  });
}

function Post({ url, pathParam = null, bodyParam = null, config = null }) {
  return fetch1({
    method: "POST",
    url,
    pathParam,
    data: bodyParam,
    config,
  });
}

function Put({ url, pathParam = null, bodyParam = null, config = null }) {
  return fetch1({
    method: "PUT",
    url: url,
    pathParam,
    data: bodyParam,
    config,
  });
}

function Delete({ url, pathParam = null, config = null }) {
  return fetch1({
    method: "DELETE",
    url: url,
    pathParam,
    config,
  });
}

export { Post, Delete, Put, Get };
