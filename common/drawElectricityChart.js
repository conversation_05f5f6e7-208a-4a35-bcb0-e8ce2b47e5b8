import { dateFormat } from "@/common/util";
import { ossStaticResourceUrl } from "@/common/constant";
import imgSrc1 from '../packageDevice/lightning-1.png';
import imgSrc2 from '../packageDevice/lightning-2.png';
function formatData(t) {
  let areas = [];
  let currentStatus = null;
  let startTime = null;

  for (let i = 0; i < t.length; i++) {
    const item = t[i];
    const time = dateFormat(item.time, 'HH:mm')

    if (item.batteryStatus !== currentStatus) {
      if (currentStatus !== null) {
        areas.push({
          startTime: startTime,
          endTime: time,
          type: currentStatus
        });
      }
      currentStatus = item.batteryStatus;
      startTime = time;
    }

    // 处理最后一条数据
    if (i === t.length - 1) {
      areas.push({
        startTime: startTime,
        endTime: '24:00',
        type: currentStatus
      });
    }
  }
  const socs = t.map((ite, ind) => {
    return ite.soc
  })
  return {
    areas,
    socs,
  };
}
function drawDashLine(t, x1, y1, x2, y2) {
  var dashLength =
    arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : 3,
    dx = x2 - x1,
    dy = y2 - y1,
    //虚线数量
    numDashes = Math.floor(Math.sqrt(dx * dx + dy * dy) / dashLength),
    u = dx / numDashes,
    f = dy / numDashes;
  t.beginPath();
  for (let i = 0; i < numDashes; i++) {
    i % 2 == 0 ? t.moveTo(x1, y1) : t.lineTo(x1, y1);
    x1 += u;
    y1 += f;
  }
  t.lineTo(x2, y2);
}
function drawRoundedBar(ctx, x, y, width, height, borderRadius, fillStyle) {
  // 绘制圆角矩形
  ctx.beginPath();
  ctx.moveTo(x + borderRadius, y);
  ctx.lineTo(x + width - borderRadius, y);
  ctx.arcTo(x + width, y, x + width, y + borderRadius, borderRadius);
  ctx.lineTo(x + width, y + height - borderRadius);
  ctx.arcTo(
    x + width,
    y + height,
    x + width - borderRadius,
    y + height,
    borderRadius
  );
  ctx.lineTo(x + borderRadius, y + height);
  ctx.arcTo(x, y + height, x, y + height - borderRadius, borderRadius);
  ctx.lineTo(x, y + borderRadius);
  ctx.arcTo(x, y, x + borderRadius, y, borderRadius);

  // 设置填充样式并填充
  ctx.fillStyle = fillStyle;
  ctx.fill();
}
class DrawCanvas {
  constructor(context) {
    console.log('[ context ] >', context)
    let { devicePixelRatio, windowWidth, screenWidth } =
      uni.getSystemInfoSync();
    const screenRatio = screenWidth / 375;
    this.dpr = devicePixelRatio;
    this.context = context;
    this.timeSegment = 24;
    // this.padding = 12 * this.dpr;
    this.padding = 0;
    this.paddingTop = 10 * this.dpr;
    this.paddingRight = 10 * this.dpr;
    this.paddingBotttom = 34 * this.dpr;
    this.tickStyle = "#000";
    this.axisStyle = "rgba(34, 34, 34, 0.08)";
    this.socs = [];
    this.areas = [];
    // this.maxPrice = getMaxPrice(this.socs);
    this.showAxisPointer = !0;
    this.mouseX = 0;
    this.mouseY = 0;
    // this.setup();
    this.width = (windowWidth - 36 / screenRatio) * this.dpr;
    this.height = 170 * this.dpr;
    this.canvasHeight = this.height - this.paddingBotttom - this.paddingTop;
    //#ifdef MP-DINGTALK
    this.canvasWidth = this.width - this.paddingRight;
    //#endif
    //#ifdef MP-WEIXIN
    this.canvasWidth = this.width - this.paddingRight - 15 * this.dpr;
    //#endif
    this.fontSize = 10 * this.dpr;
  }
  async paint() {
    this.drawXAxis();
    this.drawYAxis();
    await this.drawArea();
    this.drawElectricityBar();
    this.context.draw();
  }
  drawXAxis() {
    var t = this.context,
      //x轴间隔
      gap = this.canvasWidth / this.timeSegment;
    t.save();
    //移动坐标系原点到左下角
    t.translate(0, this.height - this.paddingBotttom);
    t.setLineWidth(1 * this.dpr);
    t.setStrokeStyle(this.axisStyle);
    t.setFillStyle(this.tickStyle);
    //以上是从左到右画X轴
    for (let n = 0; n <= this.timeSegment; n++) {
      //开始画x轴上的刻度小竖线
      if (n % 2 == 0) {
        var startX = gap * n, //起始x轴位置
          startY = 0; //起始y轴位置
        drawDashLine(
          t,
          startX,
          this.paddingBotttom,
          startX,
          -this.canvasHeight
        );
        t.stroke();
        //开始画刻度下方的数字
        t.beginPath();
        // t.font = this.font;
        t.setFontSize(this.fontSize);
        t.setTextAlign("center");
        t.setFillStyle("rgba(34, 34, 34, 0.4)");
        t.fillText(n, startX + 8 * this.dpr, this.paddingBotttom); //+20表示在y轴的下方
        t.closePath();
      }
    }
    t.restore();
  }
  drawYAxis() {
    var t = this.context,
      gap = this.canvasHeight / 4; //把Y轴刻度分成4等分
    t.save();
    t.setStrokeStyle(this.axisStyle);
    t.setFillStyle(this.tickStyle);
    for (let i = 0; i <= 4; i++) {
      var startX = 0,
        startY = i * gap + this.paddingTop,
        endX = startX + this.canvasWidth, //刻度宽度为5
        //计算Y轴的刻度值（将最大价格10等分）
        text = 100 - i * 25 + "%";
      t.beginPath();
      t.moveTo(startX, startY);
      t.lineTo(endX, startY);
      t.stroke();
      if ((i * 25) % 50 == 0) {
        // t.font = this.font;
        t.setFontSize(this.fontSize);
        t.setTextAlign("center");
        t.setFillStyle("rgba(34, 34, 34, 0.4)");
        t.textBaseline = "middle";
        t.fillText(text, endX + 14 * this.dpr, startY);
      }
      t.closePath();
    }
    t.restore();
  }
  drawElectricityBar() {
    var t = this.context;
    t.save();
    t.beginPath();
    // t.font = this.font;
    t.setFontSize(this.fontSize);

    t.setTextAlign("center");
    t.setFillStyle("rgba(34, 34, 34, 0.4)");
    for (var i = 0; i < this.socs.length; i++) {
      let item = this.socs[i],
        perWidth = this.canvasWidth / this.socs.length, //一共要显示72条柱子
        barHeightRatio = this.canvasHeight / 100,
        barHeight = item * barHeightRatio,
        barGap = 1 * this.dpr; //柱子之间的间隔
      if (item > 0) {
        drawRoundedBar(
          t,
          i * perWidth,
          this.height - this.paddingBotttom - barHeight,
          perWidth - barGap,
          barHeight,
          2 * this.dpr,
          "rgba(30, 204, 153, 1)"
        );
      }
    }
    t.restore();
  }
  drawArea() {
    var t = this.context,
      colors = [
        "rgba(0,0,0,0)",
        "rgba(253, 117, 11, 0.1)",
        "rgba(30, 204, 153, 0.1)",

      ],
      barColors = [
        "rgba(0,0,0,0)",
        "rgba(253, 117, 11, 1)",
        "rgba(30, 204, 153, 1)",


      ];
    t.save();
    return new Promise((resolve) => {
      let promiseAll = [];
      this.areas.forEach((item) => {
        console.log(item, 'item')
        let areaPromise = new Promise((resolve) => {
          var r = this.areaBox(item);
          t.setFillStyle(colors[item.type]);
          t.fillRect(r.x, r.y, r.width, r.height);
          //画底部的闪电区域
          let lightningY = this.height - this.paddingBotttom + 10 * this.dpr;
          drawRoundedBar(
            t,
            r.x,
            lightningY,
            r.width,
            8 * this.dpr,
            4 * this.dpr,
            barColors[item.type]
          );
          if (item.type == 0) {
            resolve();
          } else {
            //类型为非静置的要画闪电图
            // uni.getImageInfo({
            //   src: `/static/lightning-${item.type}.png`,
            //   success: (res) => {
            let imgWidth = 16 * this.dpr,
              imgHeight = 16 * this.dpr;
            let x = r.x + r.width / 2 - imgWidth / 2;

            //#ifdef MP-DINGTALK
            var imgUrl = item.type == 2 ? 'https://ming-enterprise-oss.mingwork.com/savePower/staticResource/lightning-1.png' : 'https://ming-enterprise-oss.mingwork.com/savePower/staticResource/lightning-2.png'
            t.drawImage(
              imgUrl,
              x,
              lightningY - 4 * this.dpr,
              imgWidth,
              imgHeight
            );
            //#endif
            //#ifdef MP-WEIXIN
            var imgUrl = item.type == 2 ? 'https://ming-enterprise-oss.mingwork.com/savePower/staticResource/lightning-1.png' : 'https://ming-enterprise-oss.mingwork.com/savePower/staticResource/lightning-2.png'
            t.save();
            // t.drawImage(imgUrl,
            //   x,
            //   lightningY - 4 * this.dpr, 
            //   imgWidth,
            //   imgHeight);
            let imgPath;
            t.drawImage(item.type == 2 ? imgSrc1 : imgSrc2,
              x,
              lightningY - 4 * this.dpr,
              imgWidth,
              imgHeight);
            // uni.getImageInfo({
            //   src: imgUrl, // 网络路径或 base64 数据
            //   success: res => {
            //     const tempFilePath = res.path; // 获取本地临时文件路径
            //     imgPath = res.path
            //     t.drawImage(imgUrl,
            //       x,
            //       lightningY - 4 * this.dpr, 
            //       imgWidth,
            //       imgHeight);
            //     }
            // })
            //#endif

            resolve();
            //   },
            //   fail: (err) => {
            //     console.error("获取图片信息失败", JSON.stringify(err));
            //   },
            // });
          }
        });
        promiseAll.push(areaPromise);
      });
      Promise.all(promiseAll).then(() => {
        t.restore();
        resolve();
      });
    });
  }
  areaBox(t) {
    var x = this.timeToPixel(t.startTime);
    return {
      x,
      y: this.paddingTop,
      width: this.timeToPixel(t.endTime) - x,
      height: this.height - this.paddingTop - 18 * this.dpr,
    };
  }

  timeToPixel(time) {
    var e = time.split(/[:：]/),
      n = (60 * e[0] + 1 * e[1]) / 1440;
    return this.padding + this.canvasWidth * n;
  }
  clear() {
    this._last_x = null;
    this._last_y = null;
    this.context.clearRect(0, 0, this.width, this.height);
  }
  update(t) {
    this.socs = t.socs || [];
    this.areas = t.areas || [];
    // this.maxPrice = getMaxPrice(t.socs);
  }
}
export { DrawCanvas, formatData };
