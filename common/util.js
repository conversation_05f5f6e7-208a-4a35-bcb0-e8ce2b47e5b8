import dayjs from "dayjs";
export function dateFormat(val, format = "YYYY-MM-DD HH:mm:ss") {
  return dayjs(val).format(format);
}
export function transformToMod5(num) {
  if (num % 5 === 0) {
    return num;
  } else if (num > 0) {
    return num + (5 - (num % 5));
  } else if (num < 0) {
    return num - (5 + (num % 5));
  }
}

export function calculateMax(arr) {
  const max = Math.max(...arr); // 找出数组中的最大值
  if(max<=0){
    return 0
  }else if(max<10){
    return 10
  }
  const len = String(max).split(".")[0].length-1;
  let result = Math.ceil(max / (10 ** len)) * (10 ** len); 
  return result;
}
export function calculateMin(arr) {
  const min = Math.min(...arr); // 找出数组中的最大值
  if(min>=0){
    return 0
  }else if(min>-10){
    return -10
  }
  const len = String(min).split(".")[0].length-2;
  let result = Math.floor(min / (10 ** len)) * (10 ** len); 
  return result;
}

//转换成万元单位
export function formatterMoneyMillion(value, unit = "", isMoney = false) {
  if (isNaN(parseFloat(value))) {
    return { value: 0, unit };
  }
  let countRules = [
    {
      unit: "",
      value: 1,
      toFixed: 0,
    },
    {
      unit: "",
      value: 1,
      toFixed: 0,
    },
    {
      unit: "",
      value: 1,
      toFixed: 0,
    },
    {
      unit: "",
      value: 1,
      toFixed: 0,
    },
    {
      unit: "",
      value: 1,
      toFixed: 0,
    },
    {
      unit: "万",
      value: 10000,
      toFixed: 2,
    },
    {
      unit: "万",
      value: 10000,
      toFixed: 2,
    },
    {
      unit: "万",
      value: 10000,
      toFixed: 2,
    },
    {
      unit: "万",
      value: 10000,
      toFixed: 2,
    },
    {
      unit: "万",
      value: 10000,
      toFixed: 2,
    },
  ];
  let moneyRules = [
    {
      unit: "",
      value: 1,
      toFixed: 2,
    },
    {
      unit: "",
      value: 1,
      toFixed: 2,
    },
    {
      unit: "",
      value: 1,
      toFixed: 2,
    },
    {
      unit: "",
      value: 1,
      toFixed: 2,
    },
    {
      unit: "",
      value: 1,
      toFixed: 2,
    },
    {
      unit: "万",
      value: 10000,
      toFixed: 2,
    },
    {
      unit: "万",
      value: 10000,
      toFixed: 2,
    },
    {
      unit: "万",
      value: 10000,
      toFixed: 2,
    },
    {
      unit: "万",
      value: 10000,
      toFixed: 2,
    },
    {
      unit: "万",
      value: 10000,
      toFixed: 2,
    },
  ];
  let rules = isMoney ? moneyRules : countRules;
  let values = (value / rules[String(~~value).length].value).toFixed(
    rules[String(~~value).length].toFixed
  );
  let units = rules[String(~~value).length].unit + unit;
  return { value: values, unit: units };
}

export function getTimeSegments(timeArray) {
  const segments = [];
  let startHour = timeArray[0].hour;
  let batteryWorkStatus = timeArray[0].batteryWorkStatus;
  for (let i = 1; i < timeArray.length; i++) {
    const currentHour = timeArray[i].hour;
    const currentStatus = timeArray[i].batteryWorkStatus;
    if (currentStatus !== batteryWorkStatus) {
      segments.push({
        startHour: startHour,
        endHour: currentHour,
        workStatus: batteryWorkStatus
      });
      startHour = currentHour;
      batteryWorkStatus = currentStatus;
    }
  }
  // Add the last time segment
  segments.push({
    startHour: startHour,
    endHour: 24,
    workStatus: batteryWorkStatus
  });

  return segments;
}
export function roundNumFun(num,decimals){
  const fraction = Math.pow(10,decimals)
  const integerPart = Math.round(num * fraction)
  let roundNum = integerPart / fraction
  if(roundNum){
      const num = roundNum.toString()
      if(num.indexOf('.')!=-1){
          const nums = num.slice(num.indexOf('.')+1)
          if(nums.length==1){
            return  roundNum+"0"
          }
          return roundNum
      }
      
  }
  return roundNum
}

export function unitConversion (num, divisor){
  if (!divisor) return 0
  if (num < divisor) return num
  // const unitNum = (num / divisor).toFixed(2)
  const unitNum = roundNumFun(num / divisor,2)
  return unitNum
}

export function alternateUnits(num, divisor){
  if (!divisor) return false
  if (num < divisor) return false
  return true
}
