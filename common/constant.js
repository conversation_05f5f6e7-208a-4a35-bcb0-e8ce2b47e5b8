export const ossStaticResourceUrl =
  "https://ming-enterprise-oss.mingwork.com/savePower/staticResource/";
export const errorMsg = {
  10040: "短信验证码错误",
  10005: "图形验证码错误",
  10109: "手机号未注册，请校验后重新输入",
  10042: "账号或密码错误，请校验后重新输入",
  10107: "手机号已经被注册",
  10108: "邮箱已经被注册",
  10110: "用户不存在",
  10044: "邮箱验证码错误",
  100206: "企业名称已存在",
};
export const worksheetStatus = [
  {
    label: "处理中",
    value: "processing",
  },
  {
    label: "已完成",
    value: "finished",
  },
];
export const worksheetTypes = [
  {
    label: "故障工单",
    text: "故障工单",
    value: "malfunction",
  },
  {
    label: "售后运营",
    text: "售后运营",
    value: "after_sale",
  },
  {
    label: "产品服务",
    text: "产品服务",
    value: "product_service",
  },
  {
    label: "交付安装",
    text: "交付安装",
    value: "delivery",
  },
  {
    label: "其他问题",
    text: "其他问题",
    value: "other",
  },
];
// export const chargeState = [
//   {
//     label: "充电中",
//     value: 1,
//     icon: 'icon-ica-dianchi-chongdian',
//     color: ""
//   },
//   {
//     label: "放电中",
//     value: 2,
//     icon: "icon-ica-dianchi-fangdian",
//     color: ""
//   },
//   {
//     label: "待机",
//     value: 0,
//     icon: "icon-ica-dianchi-lixian",
//     color: ""
//   },
//   {
//     label: "离线",
//     value: 3,
//     icon: "icon-ica-dianchi-lixian",
//     color: ""
//   }
// ];
export const chargeState = [
  {
    label: "放电中",
    value: 1,
    icon: 'icon-ica-dianchi-chongdian',
    color: ""
  },
  {
    label: "充电中",
    value: 2,
    icon: "icon-ica-dianchi-fangdian",
    color: ""
  },
  {
    label: "待机",
    value: 0,
    icon: "icon-ica-dianchi-lixian",
    color: ""
  },
  {
    label: "离线",
    value: 3,
    icon: "icon-ica-dianchi-lixian",
    color: ""
  }
];
export const barTitle = [
  {
    value: "inChargeArray",
    label: "总充电量",
    name: "总充电量(kWh)",
  }, {
    value: "disChargeArray",
    label: "总放电量",
    name: "总放电量(kWh)",
  }, {
    value: "moneyArray",
    label: "累计收益",
    name: "累计收益(元)",
  }, {
    value: "malfunctionArray",
    label: "故障分布",
    name: "故障分布",
  }, {
    value: "charge",
    label: "充电量",
    name: "充电量(kWh)",
  }, {
    value: "disCharge",
    label: "放电量",
    name: "放电量(kWh)",
  }, {
    value: "money",
    label: "累计收益",
    name: "累计收益(元)",
  }
]
export const word = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",]

export const segmentType = [
  {
    value: 1,
    label: "尖时"
  },
  {
    value: 2,
    label: "峰时"
  },
  {
    value: 3,
    label: "平时"
  },
  {
    value: 4,
    label: "谷时"
  },
  {
    value: 5,
    label: "深谷"
  },
]
export const orderTypes = [
  {
    label: '高优故障',
    value: 1
  }, {
    label: '高频故障',
    value: 2
  }, {
    label: '手动故障',
    value: 3
  }, {
    label: '手动工单',
    value: 4
  }
]
export const orderStatus = [
  {
    label: '处理中',
    value: 1,
    color: '#595959',
    iconColor: '#FF8487',
    colorOffset: '#FF4D4F',
    backGroundColor: '#EA0C28',
  }, {
    label: '已完结',
    value: 2,
    color: '#595959',
    iconColor: '#93EED2',
    colorOffset: '#5AD8A6',
    backGroundColor: '#33BE4F',
  }
]
export const alarmStatusList = [
  {
    value: 0,
    label: '待处理',
    color: '#595959',
    iconColor: '#FF4D4F',
    backGroundColor: '#EA0C28',
  },
  {
    value: 1,
    label: '已恢复',
    color: '#595959',
    iconColor: '#5AD8A6',
    backGroundColor: '#33BE4F',
  },
  {
    value: 2,
    label: '已忽略',
    color: '#595959',
    iconColor: '#76B3FF',
    backGroundColor: '#4B82FF'
  },
  {
    value: 3,
    label: '已报障',
    color: '#595959',
    iconColor: '#76B3FF',
    backGroundColor: '#8AD0FF',
  },
]
export const alarmLevelList = [
  {
    value: 1,
    label: '次要',
    color: '#7DBDFF',
    backGroundColor: 'rgba(125, 189, 255, 0.1)',
  },
  {
    value: 2,
    label: '重要',
    color: '#FE8D12',
    backGroundColor: 'rgba(254, 141, 18, 0.1)',
  },
  {
    value: 3,
    label: '紧急',
    color: '#FF696B',
    backGroundColor: 'rgba(255, 105, 107, 0.1)',

  },
]

export const chargeStateP = [
  {
    label: '充电中',
    name: '充电',
    value: "1",
    icon: "icon-ica-dianchi-chongdian",
    color: "#73ADFF",
    backGroundColor: "#73ADFF",
  },
  {
    label: '放电中',
    name: '放电',
    value: "2",
    icon: "icon-ica-dianchi-fangdian",
    color: "#33BE4F",
    backGroundColor: "#33BE4F",
  },
  {
    label: "待机",
    name: "待机",
    value: "0",
    icon: "icon-ica-dianchi-yunhang",
    color: "#FCAA6B",
    backGroundColor: "#FCAA6B",
  },
  {
    label: '离线',
    name: '离线',
    value: "3",
    icon: "icon-ica-dianchi-lixian",
    color: "var(--text-error)",
    backGroundColor: "#666666",
  },
  {
    label: '未激活',
    name: '未激活',
    value: -1,
    icon: 'icon-ica-dianchi-lixian',
    color: '#666666',
    backGroundColor: '#666666',
  },
];