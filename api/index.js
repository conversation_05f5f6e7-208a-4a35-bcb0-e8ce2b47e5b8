import apis from "./config";

let baseUrl = "";

if (process.env.NODE_ENV == "development") {
  baseUrl = 'https://ems-api-beta.ssnj.com'
  // baseUrl = 'https://ems-api.ssnj.com'
  // // #ifdef MP-DINGTALK
  // baseUrl = "https://app111777.eapps.dingtalkcloud.com";
  // // #endif
  console.log("开发环境");
} else if (process.env.NODE_ENV == "production") {
  baseUrl = 'https://ems-api.ssnj.com'
  // #ifdef MP-DINGTALK
  // baseUrl = "https://app111777.eapps.dingtalkcloud.com";
  // #endif
  console.log("生产环境");
}

console.log(process.env.NODE_ENV, 'sad');

let needBaseUrl = (url) => {
  return !(
    url.startsWith("//") ||
    url.startsWith("http") ||
    url.startsWith("https")
  );
};

function foreachApis(apis) {
  for (const key in apis) {
    let value = apis[key];
    if (typeof value == "object") {
      foreachApis(value);
    } else {
      apis[key] = needBaseUrl(value) ? baseUrl + value : value;
    }
  }
}
foreachApis(apis);
export default apis;
