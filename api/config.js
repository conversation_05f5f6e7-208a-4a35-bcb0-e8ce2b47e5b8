export default {
  DDFreeAuthLogin: "/skipAuth/DDFreeAuthLogin",
  dingTalkUserAuthAcquirePhone: "/skipAuth/dingTalkUserAuthAcquirePhone",
  bindDeviceOrg: "/device/bindDeviceOrg",
  // getAllDeviceList: "/deviceV2/getDeviceNameList",
  getCustomerInfo: "/customer/getCustomerInfo?projectName=MING_BATTERY_MONITOR",
  //工单相关
  worksheetCreate: "/worksheet/insertWorksheet",
  worksheetList: "/worksheet/page",
  worksheetDetail: "/worksheet/getWorksheetById?worksheetId={worksheetId}",
  //设备
  devicePage: "/station/page",
  getByDeviceSN: "/station/getStationInfo",
  getCabinetsBySNAndSort:
    "/device/getCabinetsBySNAndSort?deviceSn={deviceSn}&sort={sort}", //获取机柜详情
  getCustomerStationSummary: "/station/getCustomerStationSummary", // 首页统计
  // 获取设备详情柱状图
  // getSevenStatistics:
  //   "/deviceV2/statisticsDailyUsageAndProfit?deviceSn={deviceSn}&startDate={startDate}&endDate={endDate}",
  statisticsDailyUsageAndProfit: "/chargeProfit/statisticsDailyUsageAndProfit",
  getSevenStatisticsAll: "/device/getSevenStatistics",
  getBatteryDetail: "/device/getBatteryDetail?deviceSn={deviceSn}&sort={sort}",
  // 故障相关
  getFailureList: "/deviceMalfunction/page",
  ignoreWarning: "/deviceMalfunction/ignoreWarning?id={id}",
  failureDetail: "/deviceMalfunction/getMalfunctionInfo?id={id}",
  getBatteryRackList: "/station/getContainerList?stationNo={deviceSn}",
  getBatteryPackList:
    "/ems/getBatteryPackList?stationNo={deviceSn}&containerNo={sort}",
  bindDevice: "/deviceV2/bindDevice",
  queryAreaList: "/calculateElectricPrice/queryAreaList", // 查询地区列表
  getOrgAndSubOrgStationNameList: '/station/getOrgAndSubOrgStationNameList?orgId={orgId}',  // 获取所有的站点基础信息列表

  // 电池工作策略
  // getDeviceWorkProcessList:
  //   "/deviceV2/getDeviceWorkProcessList?deviceSn={deviceSn}",
  getBatteryWorkProcessDetail:
    "/deviceV2/getBatteryWorkProcessDetail?deviceSn={deviceSn}&workProcessId={workProcessId}",
  //统计单个站点24小时的电量变化
  statisticsSocAndWorkStatusByDay: "/ems/statisticsSocAndWorkStatusByDayV2?stationNo={deviceSn}&day={day}",
  // getRecommendBatteryWorkProcess:"/deviceV2/getRecommendBatteryWorkProcess?deviceSn={deviceSn}&workProcessId={workProcessId}",
  // updateBatteryWorkProcess:"/deviceV2/updateBatteryWorkProcess",
  ddFreeLogin: '/skipAuth/ddFreeLogin',
  authorizationAcquisitionPhone: '/skipAuth/authorizationAcquisitionPhone',
  getCurrentUserDetail: '/organization/getCurrentUserDetail',
  getBatteryRack: '/ems/getBmsInfo?stationNo={stationNo}&containerNo={containerNo}',
  deviceAlarmPage: '/deviceAlarm/page',
  historyPage: '/deviceAlarm/history/page',
  clearAlarm: '/deviceAlarm/clearAlarm',
  getDetail: '/deviceAlarm/getDetail',
  updateStationInfo: '/station/updateStationInfo',

  // vpp
  getVppStationInfo: '/supplier/vpp/getVppStationInfo?stationId={stationId}',
  openVppStation: '/supplier/vpp/openVppStation',
  getVppDemandRecords: '/supplier/vpp/getVppDemandRecords',
  getRealStrategyAndAiRecommendStrategy: '/strategy/getRealStrategyAndAiRecommendStrategy?stationId={stationId}&runDt={runDt}',   // 
  getStationEmsBasicInfo: 'ems/getStationEmsBasicInfo',
  statisticsDailyChargeAndProfitDetail: "/chargeProfit/statisticsDailyChargeAndProfitDetail",
  openVppAiStrategy: '/supplier/vpp/openVppAiStrategy',
  openVppDemand: '/supplier/vpp/openVppDemand',
  getConfigEarningParams: '/station/getConfigEarningParams',
  configEarningParams: '/station/configEarningParams',
  statisticsStationDateChargeAndNetProfit: '/chargeProfit/statisticsStationDateChargeAndNetProfit',
  getVppDemandTotalSettlement: '/supplier/vpp/getVppDemandTotalSettlement',
  getSubSupplierTree: '/station/getSubSupplierTree?supplierId={supplierId}&businessType={businessType}',
  statisticStationChargeAndProfitSummary: '/chargeProfit/statisticStationChargeAndProfitSummary',

  // 三方登录
  thirdUserLogin: '/skipAuth/thirdUserLogin',
  bindThirdAccount: '/skipAuth/bindThirdAccount',
  getBindThirdAccount: '/skipAuth/getBindThirdAccount',

  // 工单
  getWorkOrderPage: '/workOrder/page',
  getWorkOrderDetail: "/workOrder/detail?id={id}",
  alarmTransfer: '/workOrder/alarmTransfer',
  // 巡检
  getInspectionDevicePage: '/station/getInspectionDevicePage',
  getInspectionLogPage: '/station/getInspectionLogPage',
  addInspectionLog: '/station/addInspectionLog',

  getStaffByRole: "/organization/getStaffByRole",
  manualCreate: "/workOrder/manualCreate",
  updateWorkOrder: "/workOrder/updateWorkOrder",
};