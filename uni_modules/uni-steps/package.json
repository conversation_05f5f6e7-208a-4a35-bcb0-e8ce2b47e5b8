{"id": "uni-steps", "displayName": "uni-steps 步骤条", "version": "1.1.1", "description": "步骤条组件，提供横向和纵向两种布局格式。", "keywords": ["uni-ui", "uniui", "步骤条", "时间轴"], "repository": "https://github.com/dcloudio/uni-ui", "engines": {"HBuilderX": ""}, "directories": {"example": "../../temps/example_temps"}, "dcloudext": {"category": ["前端组件", "通用组件"], "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/@dcloudio/uni-ui"}, "uni_modules": {"dependencies": ["uni-scss", "uni-icons"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y"}, "快应用": {"华为": "u", "联盟": "u"}, "Vue": {"vue2": "y", "vue3": "y"}}}}}