<template>
	<view class="worksheet-create">
		<view class="info">
			<view class="item border">
				<text class="iconfont icon-a-ica-dianchi-guzhangbeifen3"></text><text class="label">工单类型</text>
				<view v-if="failureId" class="fromType">故障工单</view>
				<uni-data-select v-else v-model="type" :localdata="worksheetTypes" placeholder="请选择工单类型" field="label as text"></uni-data-select>
			</view>
			<view class="item">
				<text class="iconfont icon-a-ica-dianchi-guzhangbeifen15"></text><text class="label">储能站点</text><text class="deviceName">{{ deviceName }}</text>
			</view>
			<view class="content">
				<view class="title"> <text class="iconfont icon-a-ica-dianchi-guzhangbeifen5"></text><text class="second-title">问题描述</text> </view>
				<view class="">
					<uni-easyinput type="textarea" v-model="content" placeholder="请输入相关售后问题的具体描述" :maxlength="100" :inputBorder="false"></uni-easyinput>
				</view>
			</view>
		</view>
		<view class="relation">
			<view class="relation-title"><text class="iconfont icon-a-ica-dianchi-guzhangbeifen13"></text><text>联系信息</text></view>
			<view class="relation-item"
				><text class="label">企业客户</text><text class="flex-1">{{ customerInfo.corpName }}</text></view
			>
			<view class="relation-item"
				><text class="label">联系信息</text><text class="flex-1">{{ customerInfo.userName }}</text></view
			>
			<view class="relation-item"
				><text class="label">联系手机</text><text class="flex-1">{{ customerInfo.phone }}</text></view
			>
			<view class="relation-item"
				><text class="label">联系地址</text><text class="flex-1">{{ customerInfo.corpAddress }}</text></view
			>
		</view>
		<view class="btn">
			<button class="primary large" @click="submit" :disabled="submitDisabled">立即创建</button>
		</view>
	</view>
</template>
<script>
import { worksheetTypes } from "@/common/constant";
import { reactive, toRefs, computed, onBeforeMount } from "vue";
import store from "@/store/index.js"; //需要引入store
import service from "@/apiService/worksheet";
import deviceService from "@/apiService/device";
import { onShow } from "@dcloudio/uni-app";

export default {
	async onLoad(options) {
		let { deviceSn, deviceName, failureId } = options;
		if (failureId) {
			this.failureId = failureId;
			this.type = "malfunction";
			uni.showLoading();
			const result = await deviceService.failureDetail(failureId);
			let { content, deviceSn, deviceName } = result.data.data;
			this.deviceSn = deviceSn;
			this.deviceName = deviceName;
			this.content = content;
			uni.hideLoading();
		} else {
			this.deviceSn = deviceSn;
			this.deviceName = deviceName;
		}
	},
	setup() {
		const state = reactive({
			deviceSn: undefined,
			deviceName: undefined,
			type: undefined,
			content: undefined,
			failureId: undefined,
		});
		onBeforeMount(async () => {
			await store.dispatch("user/getCustomerInfo");
		});
		const customerInfo = computed(() => {
			return store.state.user.customerInfo || {};
		});
		const submitDisabled = computed(() => {
			return !(state.deviceSn && state.type && state.content);
		});
		const submit = async () => {
			uni.showLoading();
			let { corpName, customerRelationId, userName, phone, corpAddress } = customerInfo.value;
			let param = {
				...state,
				corpName,
				customerRelationId,
				relationName: userName,
				relationPhone: phone,
				corpAddress,
				deviceMalfunctionId: state.failureId,
			};
			await service.worksheetCreate(param);
			uni.hideLoading();
			uni.showToast({ icon: "success", title: "工单创建成功" });
			uni.switchTab({
				url: "/pages/workOrder/index",
			});
		};
		return {
			...toRefs(state),
			worksheetTypes,
			customerInfo,
			submit,
			submitDisabled,
		};
	},
};
</script>
<style lang="scss">
.worksheet-create {
	.uni-select {
		border: none;
		padding: 0;

		.uni-icons {
			font-size: 44rpx !important;
		}
		.uni-select__selector-item {
			font-size: 28rpx;
			line-height: 84rpx;
			height: 84rpx;
			vertical-align: middle;
			align-items: center;
			color: $uni-base-color;
		}
		.uni-select__input-text {
			color: $uni-base-color;
		}
		.uni-select__input-placeholder {
			font-size: 28rpx;
			color: $uni-help-color;
		}
	}
	.is-textarea {
		background-color: $uni-bg-color !important;
	}
	.uni-easyinput__content-textarea {
		background-color: $uni-bg-color;
		padding-left: 0;
		padding-right: 0;
		color: $uni-base-color;
	}
	.uni-easyinput__placeholder-class {
		color: $uni-help-color;
		font-size: 28rpx;
	}
}
</style>
<style lang="scss" scoped>
.worksheet-create {
	background-color: $uni-bg-color;
	padding-top: 24rpx;
}
.iconfont {
	color: $uni-main-color;
	font-size: 44rpx;
	margin-right: 16rpx;
}
.info,
.relation {
	background: #fff;
	padding: 28rpx;
}
.relation {
	margin-top: 24rpx;
	color: $uni-main-color;
	.relation-title {
		margin-bottom: 24rpx;
	}
	.relation-item {
		display: flex;
		line-height: 40rpx;
		& + .relation-item {
			margin-top: 16rpx;
		}
	}
	.label {
		color: $uni-secondary-color;
		width: 136rpx;
	}
}
.btn {
	background: #fff;
	// padding-top: 32rpx;
	width: 100%;
	// position: absolute;
	// bottom: 64rpx;
	padding-top: 130rpx;
	button {
		width: 480rpx;
		margin: 0 auto;
	}
}
.info {
	.fromType {
		flex: 1;
		text-align: right;
		color: $uni-base-color;
	}
	.item {
		margin-bottom: 32rpx;
		padding-bottom: 16rpx;
		display: flex;
		&.border {
			display: flex;
			align-items: center;
		}
		.label {
			margin-right: 32rpx;
		}
		.deviceName {
			color: $uni-base-color;
			flex: 1;
		}
	}
	.border {
		border-bottom: 1px solid $uni-border-1;
	}
	.content {
		background-color: $uni-bg-color;
		padding: 24rpx;
		color: $uni-secondary-color;
		border-radius: 16rpx;
		.title {
			margin-bottom: 20rpx;
		}
		.iconfont {
			color: $uni-secondary-color;
			font-size: 36rpx;
		}
		.second-title {
			color: $uni-secondary-color;
		}
	}
}
</style>
<style lang="scss">
.content {
	::v-deep .worksheet-create .uni-easyinput__content-textarea {
		word-break: break-all;
	}
	:deep(.uni-easyinput__content-textarea) {
		word-break: break-all;
		margin: 0;
		padding-bottom: 36rpx;
		height: 344rpx;
		color: $uni-base-color !important;
	}
}
.uni-select__selector-scroll {
	max-height: initial;
}
</style>
