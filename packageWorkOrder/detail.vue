<template>
  <view class="worksheet">
    <view class="content">
      <view class="finish" v-if="data.status == 'finished'">
        <view class="finish-icon">
          <text class="iconfont icon-a-ica-dianchi-guzhangbeifen6"></text>
        </view>
        <text>已完成</text>
      </view>
      <view class="info">
        <view class="item" v-for="item in items" :key="item.key">
          <text class="label">{{ item.label }}</text>
          <dictionary
            v-if="item.type == 'dictionary'"
            :statusOptions="item.options"
            :value="data[item.key]"
          />
          <text class="text flex-1" v-else-if="Array.isArray(item.key)">
            {{ data[item.key[0]] }}/{{ data[item.key[1]] }}
          </text>

          <text class="text flex-1" v-else>{{ data[item.key] }}</text>
        </view>
      </view>
      <view class="remark">
        <view class="title">
          <text class="iconfont icon-a-ica-dianchi-guzhangbeifen5"></text
          ><text class="label">问题描述</text></view
        >
        <view class="problem">{{ data.content }}</view>
        <view style="margin-top: 54rpx" class="title">
          <text class="iconfont icon-a-ica-dianchi-guzhangbeifen4"></text>
          <text class="label">工单备注</text></view
        >
        <view v-if="data.remark">{{ data.remark }}</view>
        <view class="extra-color" v-else>暂无备注内容</view>
      </view>
    </view>
  </view>
</template>
<script>
import { toRefs, reactive } from "vue";
import service from "@/apiService/worksheet";
import { ossStaticResourceUrl, worksheetTypes } from "@/common/constant";
import Dictionary from "@/components/dictionary.vue";

export default {
  props: { id: String },
  components: { Dictionary },
  async onLoad(options) {
    await this.getDetail(options.id);
  },
  setup(props) {
    const state = reactive({
      data: {},
    });
    const getDetail = async (id) => {
      uni.showLoading();
      let result = await service.worksheetDetail(id);
      state.data = result.data.data;
      uni.hideLoading();
    };
    return {
      ...toRefs(state),
      items: [
        { label: "工单编号", key: "workNo" },
        { label: "设备编号", key: "deviceSn" },
        {
          label: "工单类型",
          key: "type",
          type: "dictionary",
          options: worksheetTypes,
        },
        { label: "工单客户", key: "corpName" },
        { label: "工单时间", key: "createTime" },
        { label: "联系信息", key: ["relationName", "relationPhone"] },
        { label: "联系地址", key: "corpAddress" },
      ],
      getDetail,
      ossStaticResourceUrl,
    };
  },
};
</script>
<style lang="scss" scoped>
.worksheet {
  background-color: $uni-bg-color;
  padding-top: 24rpx;
  color: $uni-main-color;
  .label {
    color: $uni-secondary-color;
    margin-right: 24rpx;
  }
  .content {
    background-color: #fff;
    padding: 28rpx;
    .finish {
      margin-bottom: 36rpx;
      display: flex;
      align-items: center;
      .finish-icon {
        width: 48rpx;
        height: 48rpx;
        line-height: 48rpx;
        border-radius: 24rpx;
        background: $uni-bg-color;
        text-align: center;
        margin-right: 24rpx;
        font-size: 0;
      }
      .iconfont {
        color: $uni-primary;
        font-size: 36rpx;
      }
    }
    .info {
      .item {
        line-height: 40rpx;
        display: flex;
        & + .item {
          margin-top: 16rpx;
        }
      }
    }
    .remark {
      margin-top: 24rpx;
      padding: 24rpx;
      background: $uni-bg-color;
      border-radius: 16rpx;
      .title {
        margin-bottom: 24rpx;
        .iconfont,
        .label {
          vertical-align: middle;
        }
      }
      .iconfont {
        color: $uni-extra-color;
        font-size: 36rpx;
        margin-right: 8rpx;
      }
      .extra-color {
        color: $uni-secondary-color;
      }
      .problem {
        word-break: break-all;
      }
    }
  }
}
</style>
