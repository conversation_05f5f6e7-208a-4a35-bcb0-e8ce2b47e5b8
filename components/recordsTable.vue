<template>
	<div>
		<div class="record-1 heade-record">
			<div class="time">中标时段</div>
			<div class="power">中标容量(kWh)</div>
			<div class="price">中标价格(元/kWh)</div>
			<div class="validNgy" v-if="type == 1">有效响应容量(kWh)</div>
			<div class="profit">预计收益(元)</div>
		</div>
		<uni-collapse ref="collapse" v-model="value" @change="change" v-if="tableData.length">
			<uni-collapse-item v-for="(item, index) in tableData" :key="index" :border="false" :show-arrow="false">
				<template v-slot:title>
					<div class="record-1 list-record">
						<div class="time">
							<div>
								<div>{{ dayjs(item.runDt).format("MM/DD") }}</div>
								<div>{{ getTotalTimeSlot(item.bidPowerAndPriceList) }}</div>
							</div>
							<div>{{ "填谷" }}</div>
						</div>
						<div class="power">
							{{ item.bidPower }}
						</div>
						<div class="price">
							{{ (getTotalPrice(item.bidPowerAndPriceList) || 0).toFixed(2) }}
						</div>
						<div class="validNgy" v-if="type == 1">
							{{ item.validNgy }}
						</div>
						<div class="profit">
							{{ item.predictAmount }}
						</div>
					</div>
				</template>
				<view class="">
					<div v-for="(ite, ind) in item.bidPowerAndPriceList" :key="ind" class="child">
						<div class="record-1 child-record">
							<div class="time child-time">
								<div>
									{{ ite.startTime.slice(0, 5) + "-" + ite.endTime.slice(0, 5) }}
								</div>
								<div>{{ "填谷" }}</div>
							</div>
							<div class="power">
								{{ ite.bidPower }}
							</div>
							<div class="price">
								{{ (ite.bidPrice || 0).toFixed(2) }}
							</div>
							<div class="validNgy" v-if="type == 1"></div>
							<div class="profit">
								{{ ite.bidAmount }}
							</div>
						</div>
					</div>
				</view>
			</uni-collapse-item>
		</uni-collapse>
		<view v-else class="empty">
			<empty :value="type == 1 ? '没有已响应记录' : '没有待响应记录'" style="width: 100%" />
		</view>
	</div>
</template>

<script setup>
import dayjs from "dayjs";
import { ref } from "vue";
import empty from "./empty.vue";
const props = defineProps({
	tableData: {
		type: Array,
		default: () => [],
	},
	tableColumns: {
		type: Array,
		default: () => [],
	},
	type: {
		type: Number,
		default: 0,
	},
});
const collapse = ref();
const value = ref();
const change = () => {};
const extraIcon = ref({
	color: "#4cd964",
	size: "26",
	type: "image",
});
const getTotalTimeSlot = (arr) => {
	return arr[0].startTime.slice(0, 5) + "-" + arr[arr.length - 1].endTime.slice(0, 5);
};
const getTotalPrice = (arr) => {
	return arr.reduce((a, b) => a + b.bidPrice, 0);
};
</script>

<style lang="scss" scoped>
.record-1 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	text-align: center;
	font-size: 24rpx;
	padding: 12rpx 16rpx;

	color: rgba(34, 34, 34, 0.65);

	&.child-record {
		// padding-left: 24rpx;
		padding-top: 0;
		// padding-bottom: 0;
		// padding-right: 72rpx;
	}
	&.list-record {
	}
	&.heade-record {
		// padding-right: 72rpx;
		// padding-left: 40rpx;
	}
	.time {
		width: 220rpx;
		text-align: left;
		display: flex;
		justify-content: space-between;
		align-items: center;
		color: rgba(34, 34, 34, 0.65);
		&.child-time {
			// padding-left: 44rpx;
		}
	}
	.power {
		width: 120rpx;
		text-align: center;
	}
	.price {
		width: 120rpx;
		text-align: center;
	}
	.validNgy {
		// width: 120rpx;
	}
	.profit {
		width: 100rpx;
		text-align: center;
	}
	.child {
		width: 100%;
		flex: 1;
	}
}
:deep(.uni-collapse-item) {
	background: rgba(34, 34, 34, 0.04);
	border-radius: 8px;
	margin-top: 12rpx;
}
:deep(.uni-collapse-item__title.uni-collapse-item-border) {
	border-bottom: none;
}
:deep(.uni-collapse-item__wrap) {
	background: none;
}
:deep(.uni-collapse-item__wrap-content.uni-collapse-item--border) {
	border: none;
}
</style>
