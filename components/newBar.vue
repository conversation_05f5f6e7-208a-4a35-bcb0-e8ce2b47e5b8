<template>
	<view>
		<!-- 	@touchend="tap" -->
		<!-- <canvas
			:style="{
				width: width + 'rpx',
				height: height + 'rpx',
			}"
			canvas-id="id996rnmsb"
			id="id996rnmsb"
			type="2d"
			class="charts"
			:width="cWidth * pixelRatio"
			:height="cHeight * pixelRatio" /> -->
		<!-- #ifdef MP-DINGTALK -->
		<canvas canvas-id="id996rnmsb" id="id996rnmsb" type="2d" :style="{ width: cWidth + 'px', height: cHeight + 'px' }" :width="cWidth * pixelRatio" :height="cHeight * pixelRatio" class="charts" />
		<!-- #endif -->
		<!-- #ifdef MP-WEIXIN -->
		<canvas canvas-id="id996rnmsb" id="id996rnmsb" type="2d" class="charts" :style="{ width: cWidth + 'px', height: cHeight + 'px' }" :width="cWidth * pixelRatio" :height="cHeight * pixelRatio" />
		<!-- #endif -->
	</view>
</template>

<script>
import { onBeforeMount, onMounted, reactive, toRefs, watch, computed, getCurrentInstance } from "vue";
import uCharts from "@qiun/ucharts/u-charts.js";
import { transformToMod5, calculateMax, calculateMin } from "@/common/util";
import { barTitle } from "@/common/constant";
var uChartsInstance = {};
export default {
	props: {
		fromType: {
			type: String,
			default: () => "",
		},
		chartData: {
			type: Array,
			default: () => [],
		},
		dateTime: {
			type: Array,
			default: () => [],
		},
		width: {
			type: String,
			default: "646",
		},
		height: {
			type: String,
			default: "400",
		},
	},
	setup(props) {
		const { chartData, dateTime, fromType, width, height } = toRefs(props);
		const state = reactive({
			cWidth: 646,
			cHeight: 400,
			pixelRatio: 1,
		});
		const getServerData = () => {
			uni.showLoading();
			//模拟从服务器获取数据时的延时
			setTimeout(() => {
				//模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
				let res = {
					series: chartData.value,
				};
				drawCharts("id996rnmsb", res);
				uni.hideLoading();
			}, 501);
		};
		onMounted(async () => {
			//这里的 750 对应 css .charts 的 width
			state.cWidth = uni.upx2px(width.value);
			//这里的 500 对应 css .charts 的 height
			state.cHeight = uni.upx2px(height.value);
			state.pixelRatio = uni.getSystemInfoSync().pixelRatio;
		});

		watch(fromType, (val) => {
			setTimeout(() => {
				getServerData();
			}, 501);
		});
		const instance = getCurrentInstance();
		const drawCharts = (id, data) => {
			let max0;
			let min0;
			let max1;
			let min1;

			const arr0 = [...(data.series[0]?.data || []), ...(data.series[1]?.data || [])];
			const arr1 = [...(data.series[2].data || [])];
			max0 = calculateMax(arr0);
			min0 = calculateMin(arr0);
			max1 = calculateMax(arr1);
			min1 = calculateMin(arr1);

			if (max0 == 0 && min0 == 0 && max1 == 0 && min1 == 0) {
				max0 = 10;
				min0 = 0;
				max1 = 10;
				min1 = 0;
			}
			//   let max = calculateMax(chartData.value);
			//   let min = calculateMin(chartData.value);

			//#ifdef MP-DINGTALK
			const ctx = uni.createCanvasContext(id);
			uChartsInstance[id] = new uCharts({
				type: "mix",
				context: ctx,
				width: state.cWidth * state.pixelRatio,
				height: state.cHeight * state.pixelRatio,
				categories: dateTime.value,
				series: data.series,
				animation: true,
				background: "#FFFFFF",
				color: ["#1ECC99", "#1ECC99"],
				padding: [15, 5, 0, 5],
				enableScroll: false,
				pixelRatio: state.pixelRatio,
				dataLabel: false,
				legend: {},
				xAxis: {
					disableGrid: true,
				},
				yAxis: {
					gridType: "dash",
					dashLength: "2",
					showTitle: true,
					disabled: false,
					disableGrid: false,
					data: [
						{
							max: max0,
							min: min0,
							position: "left",
							title: "kWh",
							titleOffsetY: -5,
							titleOffsetX: -25,
							axisLine: false,
						},
						{
							max: max1,
							min: min1,
							position: "right",
							title: "元",
							titleOffsetY: -5,
							titleOffsetX: 30,
							axisLine: false,
						},
					],
				},
				extra: {
					//   column: {
					//     type: "group",
					//     width: 60 * state.pixelRatio,
					//     activeBgColor: "#000000",
					//     activeBgOpacity: 0.08,
					//     // linearType: "custom",
					//     // seriesGap: 5,
					//     // linearOpacity: 1,
					//     customColor: ["#1ECC99", "#1ECC99"],
					//     categoryGap: 10,
					//     barBorderCircle: false,
					//     // barBorderRadius: [0, 0, 0, 0],
					//   },
					mix: {
						column: {
							type: "group",
							width: 12,
							// barBorderCircle: true,
							barBorderRadius: [6, 6, 6, 6],
							seriesGap: 2,
						},
					},
				},
			});
			//#endif
			//#ifdef MP-WEIXIN
			const query = uni.createSelectorQuery().in(instance).select("#id996rnmsb");
			query.fields({ node: true, size: true }).exec((res) => {
				const canvas = res[0].node;
				const ctx = canvas.getContext("2d");
				canvas.width = state.cWidth * state.pixelRatio; // 设置实际 canvas 的宽度
				canvas.height = state.cHeight * state.pixelRatio; // 设置实际 canvas 的高度

				uChartsInstance[id] = new uCharts({
					type: "mix",
					context: ctx,
					width: state.cWidth * state.pixelRatio,
					height: state.cHeight * state.pixelRatio,
					categories: dateTime.value,
					series: data.series,
					animation: true,
					background: "#FFFFFF",
					color: ["#1ECC99", "#1ECC99"],
					padding: [15, 5, 0, 5],
					enableScroll: false,
					pixelRatio: state.pixelRatio,
					dataLabel: false,
					legend: {},
					xAxis: {
						disableGrid: true,
					},
					yAxis: {
						gridType: "dash",
						dashLength: "2",
						showTitle: true,
						disabled: false,
						disableGrid: false,
						data: [
							{
								max: max0,
								min: min0,
								position: "left",
								title: "kWh",
								titleOffsetY: -5,
								titleOffsetX: -25,
								axisLine: false,
							},
							{
								max: max1,
								min: min1,
								position: "right",
								title: "元",
								titleOffsetY: -5,
								titleOffsetX: 30,
								axisLine: false,
							},
						],
					},
					extra: {
						mix: {
							column: {
								type: "group",
								width: 12,
								// barBorderCircle: true,
								barBorderRadius: [6, 6, 6, 6],
								seriesGap: 2,
							},
						},
					},
				});
			});
			//#endif
		};
		return {
			...toRefs(state),
			getServerData,
			drawCharts,
			barTitle,
		};
	},
};
</script>

<style scoped>
.charts {
	/* 判断是否是微信小程序 */
	/* #ifdef MP-WEIXIN */
	/* width: 646rpx;
	height: 400rpx; */
	/* #endif */
}
</style>
