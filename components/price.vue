<template>
  <span v-if="isMoney">
    <span>{{ formatterMoneyMillion(data).value }}</span>
    <span>{{ formatterMoneyMillion(data).unit }}</span>
  </span>
  <span v-else>{{ data }}</span>
</template>

<script>
import { formatterMoneyMillion } from "@/common/util";
export default {
  props: {
    data: Number,
    isMoney: Boolean,
  },
  setup(props) {
    return {
      formatterMoneyMillion,
    };
  },
};
</script>

<style lang="scss" scoped></style>
