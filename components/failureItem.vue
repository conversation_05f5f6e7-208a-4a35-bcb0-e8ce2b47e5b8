<template>
  <view class="box">
    <view class="top">
      <view class="flex-1 title">
        <view class="icon"
          ><i class="iconfont icon-ica-dianchi-guzhang"></i
        ></view>
        <view class="text">故障报警</view>
      </view>
      <view class="ignore" v-if="data.ignoreFlag == 0" @click="ignore"
        >忽略</view
      >
    </view>
    <view class="mid">
      <view class="title">{{ data.content }}</view>
      <view class="time">时间：{{ data.createTime }}</view>
    </view>
    <view class="bot">
      <view class="name">
        <i class="iconfont icon-ica-shouye-1beifen"></i>
        <text class="text overflow">{{ data.deviceName }}</text>
      </view>
      <view class="create-btn" @click="createOrder" v-if="data.ignoreFlag == 0"
        >创建工单</view
      >
    </view>
  </view>
</template>
<script>
import { toRefs } from "vue";
import service from "@/apiService/device";
export default {
  props: {
    data: Object,
  },
  setup(props, { emit }) {
    const { data } = toRefs(props);
    const ignore = () => {
      uni.showModal({
        title: "提示",
        content: "是否确定忽略当前故障报警？忽略后故障报警将不可恢复！",
        cancelText: "取消",
        confirmText: "确认",
        success: async function (res) {
          if (res.confirm) {
            // 这里要做一个操作传递一个事件到列表ß
            await service.ignoreWarning(data.value.id);
            uni.showToast({
              title: "操作成功",
              icon: "success",
            });
            emit("finish");
          } else if (res.cancel) {
          }
        },
      });
    };
    const createOrder = () => {
      uni.navigateTo({
        // 这里参数还要加上问题描述
        url: `/packageWorkOrder/create?failureId=${data.value.id}`,
      });
    };
    return {
      ignore,
      createOrder,
    };
  },
};
</script>

<style lang="scss" scoped>
.box {
  padding: 32rpx 28rpx;
  background: #fff;
}
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  .title {
    display: flex;
    align-items: center;
    color: $uni-main-color;
  }
  .icon {
    width: 48rpx;
    height: 48rpx;
    text-align: center;
    line-height: 48rpx;
    border-radius: 24rpx;
    background: #f6f7f7;
    margin-right: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .iconfont {
    font-size: 36rpx;
    color: $uni-error;
  }
  .text {
    font-size: 28rpx;
    line-height: 48rpx;
  }
  .ignore {
    width: 112rpx;
    height: 56rpx;
    background: #f6f7f7;
    border-radius: 28rpx;
    line-height: 56rpx;
    font-size: 28rpx;
    text-align: center;
    color: $uni-secondary-color;
  }
}
.mid {
  margin-bottom: 24rpx;
  .title {
    font-size: 32rpx;
    line-height: 48rpx;
    color: $uni-main-color;
    margin-bottom: 8rpx;
    font-weight: 600;
    word-break: break-all;
  }
  .time {
    font-size: 28prx;
    color: $uni-secondary-color;
    line-height: 44rpx;
  }
}
.bot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .name {
    display: flex;
    align-items: center;
    flex: 1;
    padding-right: 24rpx;
    width: 0;
    .iconfont {
      font-size: 32rpx;
      line-height: 32rpx;
      margin-right: 8rpx;
      color: $uni-help-color;
    }
    .text {
      font-size: 28rpx;
      line-height: 40rpx;
      color: $uni-secondary-color;
    }
  }
  .create-btn {
    font-size: 28rpx;
    line-height: 56rpx;
    color: #fff;
    width: 156rpx;
    height: 56rpx;
    text-align: center;
    background: $uni-primary;
    border-radius: 8rpx;
  }
}
</style>
