<template>
  <view style="height: 750rpx">
    <l-echart ref="chart"></l-echart>
  </view>
</template>
<script setup>
// 或者按需引入
// import * as echarts from "@/uni_modules/lime-echart/static/echarts.min";
import { ref, onMounted, watch } from "vue";
//#ifdef MP-DINGTALK
// const echarts = require("../uni_modules/lime-echart/static/echarts.min");
//#endif
//#ifdef MP-WEIXIN
const echarts = require("./packageEcharts/lime-echart/static/echarts.min");
//#endif
const props = defineProps({
  chartData: {
    type: Array,
    default: () => [],
  },
});
const options = {
  title: {
    text: "",
  },
  tooltip: {
    trigger: "axis",
  },
  color: ["rgba(22, 119, 255, 1)", "rgba(255, 112, 0,1)"],
  legend: {
    icon: "rect",
    itemWidth: 10,
    itemHeight: 10,
    data: ["今日实际运行功率", "今日预测运行功率"],
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    boundaryGap: true,
    axisTick: {
      alignWithLabel: true,
    },
    axisLabel: {
      width: 100,
      overflow: "truncate",
      formatter(v) {
        return " ".repeat(3) + v + " ".repeat(3);
      },
    },
    data: [
      "00:00",
      "00:15",
      "00:30",
      "00:45",
      "01:00",
      "01:15",
      "01:30",
      "01:45",
      "02:00",
      "02:15",
      "02:30",
      "02:45",
      "03:00",
      "03:15",
      "03:30",
      "03:45",
      "04:00",
      "04:15",
      "04:30",
      "04:45",
      "05:00",
      "05:15",
      "05:30",
      "05:45",
      "06:00",
      "06:15",
      "06:30",
      "06:45",
      "07:00",
      "07:15",
      "07:30",
      "07:45",
      "08:00",
      "08:15",
      "08:30",
      "08:45",
      "09:00",
      "09:15",
      "09:30",
      "09:45",
      "10:00",
      "10:15",
      "10:30",
      "10:45",
      "11:00",
      "11:15",
      "11:30",
      "11:45",
      "12:00",
      "12:15",
      "12:30",
      "12:45",
      "13:00",
      "13:15",
      "13:30",
      "13:45",
      "14:00",
      "14:15",
      "14:30",
      "14:45",
      "15:00",
      "15:15",
      "15:30",
      "15:45",
      "16:00",
      "16:15",
      "16:30",
      "16:45",
      "17:00",
      "17:15",
      "17:30",
      "17:45",
      "18:00",
      "18:15",
      "18:30",
      "18:45",
      "19:00",
      "19:15",
      "19:30",
      "19:45",
      "20:00",
      "20:15",
      "20:30",
      "20:45",
      "21:00",
      "21:15",
      "21:30",
      "21:45",
      "22:00",
      "22:15",
      "22:30",
      "22:45",
      "23:00",
      "23:15",
      "23:30",
      "23:45",
      "24:00",
    ],
  },
  yAxis: {
    type: "value",
    name: "功率(kW)",
    nameTextStyle: {
      align: "center",
    },
  },
  series: [
    {
      name: "今日实际运行功率",
      type: "line",
      step: "start",
      symbol: "none",
      symbolSize: 0,
      data: [],
      silent: true,
      markArea: {
        silent: true,
      },
      emphasis: {
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: "rgba(22, 119, 255, 1)",
            },
            {
              offset: 1,
              color: "rgba(178, 210 ,255,1)",
            },
          ]),
        },
        focus: "self",
      },
      select: {
        disabled: false,
      },
      showSymbol: false,
      areaStyle: {
        opacity: 0.4,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: "rgba(22, 119, 255, 1)",
          },
          {
            offset: 1,
            color: "rgba(178, 210 ,255,1)",
          },
        ]),
      },
    },
    {
      name: "今日预测运行功率",
      type: "line",
      step: "start",
      symbol: "none",
      symbolSize: 0,
      data: [],
      emphasis: {
        itemStyle: {},
        disabled: false,
      },
      areaStyle: {
        opacity: 0.4,
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: "rgba(255, 112, 0,1)",
          },
          {
            offset: 1,
            color: "rgba(255, 228, 207,1)",
          },
        ]),
      },
    },
  ],
};
const chart = ref();
onMounted(() => {});
watch(
  () => props.chartData,
  (newVal) => {
    if (newVal) {
      options.series[0].data = newVal.realStrategySegments.map((item) => {
        return item.power;
      });
      options.series[1].data = newVal.aiRecommendStrategySegments.map(
        (item) => {
          return item.power;
        }
      );
      chart.value.init(echarts, (chart) => {
        chart.setOption(options);
      });
    }
  }
);
</script>

<style lang="scss" scoped></style>
