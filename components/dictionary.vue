<template>
	<view v-if="statusObj.color && showBadge" class="dictionary">
		<span v-if="isTextColor" :style="{ color: statusObj.color }" style="font-size: 28rpx">
			{{ statusObj[labelKey] }}
		</span>
		<span
			v-else-if="backgroundColor"
			class="isBackgroundColor"
			:style="{
				background: statusObj.backGroundColor ? statusObj.backGroundColor : 'rgba(0,0,0,.1)',
				color: statusObj.color ? statusObj.color : '#141414',
			}">
			{{ statusObj[labelKey] }}
		</span>
		<!-- <a-badge v-else :color="statusObj.color" :text="statusObj[labelKey]" /> -->
		<span v-else-if="text">
			<span style="font-size: 28rpx">{{ statusObj[labelKey] }}</span>
		</span>
		<span v-else>
			<b
				class="iconBadge"
				:style="{
					backgroundColor: statusObj.iconColor,
					boxShadow: `0px 0px 8rpx 0px ${statusObj.shadowColor}`,
				}"></b>
			<span class="text" :style="{ color: statusObj.color }">{{ statusObj[labelKey] }}</span>
		</span>
	</view>
	<text v-else> {{ statusObj[labelKey] }}</text>
</template>
<script>
import { toRefs, computed, watch, toRef, reactive } from "vue";
export default {
	props: {
		statusOptions: {
			type: Array,
			default: () => [],
		},
		value: String,
		labelKey: {
			type: String,
			default: "label",
		},
		isTextColor: {
			type: Boolean,
			default: false,
		},
		isBackgroundColor: {
			type: Boolean,
			default: false,
		},
		text: {
			type: Boolean,
			default: false,
		},
		showBadge: {
			type: Boolean,
			default: true,
		},
	},
	setup(props) {
		const { statusOptions, value, isBackgroundColor, text } = toRefs(props);
		const statusObj = computed(() => {
			const item = statusOptions.value.find((s) => s.value === value.value) || {};
			return item;
		});

		const state = reactive({
			backgroundColor: false,
		});
		watch(
			isBackgroundColor,
			(val) => {
				state.backgroundColor = val;
			},
			{ immediate: true }
		);
		return {
			statusObj,
			...toRefs(state),
		};
	},
};
</script>
<style lang="scss" scoped>
.badge {
	display: flex;
	align-items: center;
	.circle {
		width: 20rpx;
		height: 20rpx;
		background: rgba(255, 125, 0, 1);
		box-shadow: 0px 0px 8rpx 0px rgba(255, 125, 0, 0.5);
		border: 1px solid #ffffff;
		border-radius: 50%;
		margin-right: 8rpx;
	}
}
.isBackgroundColor {
	display: inline-block;
	height: 100%;
	line-height: 44rpx;
	padding: 0 16rpx;
	font-size: 24rpx;
}
.iconBadge {
	display: inline-block;
	width: 24rpx;
	height: 24rpx;
	border-radius: 50%;
	border: 1px solid #ffffff;
	vertical-align: middle;
	margin-right: 8rpx;
}
.text {
	vertical-align: middle;
}
.dictionary {
	span {
		font-size: 24rpx;
	}
}
</style>
