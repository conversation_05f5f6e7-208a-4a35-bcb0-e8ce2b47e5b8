<template>
  <scroll-view
    :scroll-y="scrollY"
    :scroll-x="scrollX"
    class="scroll-view"
    :scroll-left="scrollLeft"
    :scroll-into-view="scrollIntoView"
  >
    <view class="tabs">
      <view
        v-for="(item, index) in options"
        :key="item[config.value]"
        class="tab"
        :class="[
          activeKey == item[config.value] ? 'selected' : '',
          textColor ? 'textColor' : '',
        ]"
        @click="onChange(item[config.value], index)"
        :id="'tab' + item[config.value]"
        >{{ item[config.label] }}</view
      >
    </view>
  </scroll-view>
</template>
<script>
export default {
  name: "tabsFilter",
  props: {
    selectedValue: "",
    options: {
      type: Array,
      default: () => [],
    },
    config: {
      type: Object,
      default: () => {
        return { label: "label", value: "value" };
      },
    },
    scrollX: {
      type: <PERSON>olean,
      default: false,
    },
    scrollY: {
      type: <PERSON>olean,
      default: false,
    },
    textColor: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeKey: this.options[0]?.[this.config.value],
      scrollLeft: 0,
      scrollIntoView: "tab",
      currentIndex: 0,
    };
  },
  methods: {
    onChange(value, index) {
      this.activeKey = value;
      if (this.options.length > 4) {
        this.scrollIntoView = "tab" + this.options[index].value;
        this.currentIndex = index;
      }
      this.$emit("change", value, index);
    },
  },
  watch: {
    selectedValue: {
      handler(newVal, oldVal) {
        this.activeKey = newVal;
      },
      immediate: true,
    },
  },
};
</script>
<style lang="scss">
.tabs {
  padding: 24rpx;
  white-space: nowrap;
  display: flex;
  line-height: 40rpx;
  .tab {
    // display: inline;
    padding-bottom: 16rpx;
    color: $uni-secondary-color;
    & + .tab {
      margin-left: 40rpx;
    }
    &.selected {
      color: $uni-main-color;
      position: relative;
      font-weight: bold;
      &.textColor {
        color: $uni-primary;
      }
      &::after {
        content: "";
        height: 8rpx;
        width: 48rpx;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        background-color: $uni-primary;
        border-radius: 4rpx;
      }
    }
    &:last-child {
      margin-right: 24rpx;
    }
  }
}
</style>
