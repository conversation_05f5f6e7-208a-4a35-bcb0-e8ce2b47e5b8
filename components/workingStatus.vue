<template>
	<div class="w-full h-full relative text-center text-sm">
		<!-- 圈1 -->
		<div class="w-28 absolute left-0 top-0">
			<div class="relative circle circle-1">
				<div class="circle-box circle-box1 rounded-full border border-border">
					<div class="leading-5">
						<div class="mx-auto w-8 h-8 mb-1">
							<img src="@/static/dw.svg" class="iconSvg" />
						</div>
						<div v-show="status !== 3 && gridPower != 0">
							{{ gridPower }}
						</div>
						<div v-show="status !== 3 && gridPower != 0">kW</div>
					</div>
				</div>
			</div>

			<div class="mt-5 leading-5">电网</div>
		</div>

		<!-- 圈2 -->
		<div class="absolute right-0 top-0">
			<div class="relative circle circle-2">
				<div class="circle-box circle-box2 rounded-full border border-border">
					<div class="leading-5">
						<div class="mx-auto w-8 h-8 mb-1">
							<img src="@/static/cn.svg" class="iconSvg" />
						</div>
						<div v-show="status !== 3">
							{{ Math.abs(bmsPower) > 1 ? bmsPower : 0 }}
						</div>
						<div v-show="status !== 3">kW</div>
					</div>
				</div>
			</div>
			<div class="mt-5 leading-5">储能</div>
		</div>

		<!-- 圈3 -->
		<div class="w-28 absolute left-half -ml-14 bottom-5">
			<div class="relative circle circle-3">
				<div class="circle-box circle-box3 rounded-full border border-border">
					<div class="leading-5">
						<div class="mx-auto w-8 h-8 mb-1">
							<img src="@/static/fz.svg" class="iconSvg" />
						</div>
					</div>
				</div>
			</div>
			<div class="mt-5 leading-5">负载</div>
		</div>

		<!-- 线框 -->
		<div class="absolute z-20" style="width: calc(100% - 280rpx); height: calc(100% - 280rpx); left: 140rpx; bottom: 210rpx">
			<!-- 线1 -->
			<div class="absolute w-half h-2 left-0 -top-1 overflow-hidden">
				<!-- 虚线 -->
				<div class="absolute w-full left-0 top-1 border-b border-dashed" style="border-color: #e1b5fe"></div>
				<!-- 箭头1 -->
				<div v-if="gridPower !== 0 && status !== 3" class="w-10 h-0 direction absolute top-1" :class="gridPower > 0 ? 'direction-dw-reverse' : 'direction-dw'" style="border-color: #e1b5fe">
					<div class="absolute w-0 dw-arrow"></div>
				</div>
			</div>
			<!-- 中心点 -->
			<div
				class="absolute left-half top-0 z-10"
				style="width: 7px; height: 7px; border: 1px solid #d0d0d0; border-radius: 50%; background: #fff; margin-left: -3px; margin-top: -3px; z-index: 10"></div>
			<!-- 线2 -->
			<div class="absolute w-half h-2 right-0 -top-1 overflow-hidden">
				<div class="absolute w-full left-0 top-1 border-b border-dashed" style="border-color: #71d6d7"></div>
				<!-- 箭头2 -->
				<div
					v-if="Math.abs(bmsPower) > 1 && status !== 3 && bmsPower !== 0"
					class="w-10 h-0 direction absolute top-1"
					:class="bmsPower > 0 ? 'direction-cn-reverse' : bmsPower < 0 ? 'direction-cn' : ''"
					style="border-color: #71d6d7">
					<div class="absolute w-0 h-2 cn-arrow"></div>
				</div>
			</div>
			<!-- 线3 -->
			<div class="absolute w-2 h-full left-half -ml-1 top-0 overflow-hidden">
				<div class="absolute h-full left-1 top-0 border-r border-dashed" style="border-color: #a6e0fb"></div>
				<!-- 箭头3 -->
				<div class="h-10 w-0 border-r absolute left-1 top-0" style="border-color: #a6e0fb" :class="status === 3 ? 'border-none' : 'direction-b'">
					<div class="absolute fz-arrow"></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { watch } from "vue";
export default {
	props: {
		bmsPower: Number,
		gridPower: Number,
		status: Number,
	},
	setup(props) {
		watch(props, () => {
			// 监听数据，如果bmsPower的绝对值小雨
		});
		return {};
	},
};
</script>

<style lang="less" scoped>
// 添加左右移动动画
@keyframes arrowLMoveToLeft {
	0% {
		right: -40rpx;
	}
	100% {
		right: 100%;
	}
}
@keyframes arrowLMoveToRight {
	0% {
		left: -40rpx;
	}
	100% {
		left: 100%;
	}
}
.cn-arrow {
	width: 0;
	height: 7px;
	border-top: 3px solid transparent;
	border-bottom: 3px solid transparent;
	margin-top: -3px;
}
.direction-cn {
	animation: arrowLMoveToLeft 3s linear infinite;
	.cn-arrow {
		border-right: 6px solid #71d6d7;
		left: 0;
	}
}
.direction-cn-reverse {
	animation: arrowLMoveToRight 3s linear infinite;
	.cn-arrow {
		border-left: 6px solid #71d6d7;
		right: 0;
	}
}
@keyframes arrowRMoveToLeft {
	0% {
		right: -40rpx;
	}
	100% {
		right: 100%;
	}
}
@keyframes arrowRMoveToRight {
	0% {
		left: -40rpx;
	}
	100% {
		left: 100%;
	}
}

.dw-arrow {
	width: 0;
	height: 7px;
	border-top: 3px solid transparent;
	border-bottom: 3px solid transparent;
	margin-top: -3px;
}
.direction-dw {
	animation: arrowRMoveToLeft 3s linear infinite;
	.dw-arrow {
		border-right: 6px solid #e1b5fe;
		left: 0;
	}
}
.direction-dw-reverse {
	left: 0;
	animation: arrowRMoveToRight 3s linear infinite;
	.dw-arrow {
		border-left: 6px solid #e1b5fe;
		right: 0;
	}
}

@keyframes arrowBMoveToBottom {
	0% {
		top: -40rpx;
	}
	100% {
		top: 100%;
	}
}
@keyframes arrowBMoveToTop {
	0% {
		top: 100%;
	}
	100% {
		top: -40rpx;
	}
}

.fz-arrow {
	width: 7px;
	height: 0;
	border-left: 3px solid transparent;
	border-right: 3px solid transparent;
	margin-left: -3px;
}
.direction-b {
	animation: arrowBMoveToBottom 2s linear infinite;
	.fz-arrow {
		border-top: 6px solid #a6e0fb;
		bottom: 0;
	}
}
.direction-b-reverse {
	animation: arrowBMoveToTop 2s linear infinite;
	.fz-arrow {
		border-bottom: 6px solid #a6e0fb;
		top: 0;
	}
}
// 添加放大缩小动画
@keyframes scaleToLg {
	0% {
		transform: scale(1.3);
		opacity: 0.4;
	}
	50% {
		transform: scale(1);
		opacity: 1;
	}
	100% {
		transform: scale(1.3);
		opacity: 0.4;
	}
}

.circle {
	width: 140rpx;
	height: 140rpx;
	position: relative;
	background: #fff;
	z-index: 2;
	left: 50%;
	transform: translateX(-50%);
	&::after {
		display: block;
		content: "";
		width: 100%;
		height: 100%;
		border-radius: 50%;
		border: 1px solid #f2f2f2;
		position: absolute;
		left: -1px;
		top: -1px;
		animation: scaleToLg 2s linear infinite;
		z-index: 0;
		opacity: 1;
	}
	&.circle-1 {
		&::after {
			border-color: #f7ecfe;
			background: #fdf9ff;
		}
	}
	&.circle-2 {
		&::after {
			border-color: #eaffe6;
			background: #fdfffc;
		}
	}
	&.circle-3 {
		&::after {
			border-color: #ebf4ff;
			background: #f9fdff;
		}
	}
	.circle-box {
		width: 140rpx;
		height: 140rpx;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px solid #f2f2f2;
		z-index: 999;
		&.circle-box1 {
			border-color: #e1b5fd;
		}
		&.circle-box2 {
			border-color: #71d6d7;
		}
		&.circle-box3 {
			border-color: #a6e0fb;
		}
	}
}
.relative {
	position: relative;
}
.absolute {
	position: absolute;
}
.left-0 {
	left: 0;
}
.top-0 {
	top: 0;
}
.right-0 {
	right: 0;
}
.bottom-5 {
	bottom: 40rpx;
}
.rounded-full {
	border-radius: 9999px;
}
.border {
	border-width: 1px;
}
.border-border {
	border-color: #d9d9d9;
}
.leading-5 {
	line-height: 32rpx;
}
.w-full {
	width: 100%;
}
.h-full {
	height: 100%;
}
.text-center {
	text-align: center;
}
.w-8 {
	width: 32rpx;
}
.h-8 {
	width: 32rpx;
}
.mb-1 {
	margin-bottom: 4rpx;
}
.w-28 {
	width: 112rpx;
}
.left-half {
	left: 50%;
}
.-ml-14 {
	margin-left: -56rpx;
}
.mt-5 {
	margin-top: 20rpx;
}
.w-half {
	width: 50%;
}
.h-2 {
	height: 8px;
}
.-top-1 {
	top: -4px;
}
.overflow-hidden {
	overflow: hidden;
}
.top-1 {
	top: 4px;
}

.border-b {
	border-bottom-width: 1px;
}
.border-dashed.border-b {
	border-bottom: 1px dashed;
}
.border-r.border-dashed {
	border-right: 1px dashed;
}
.border-solid {
	border-style: solid;
}
.border-none {
	border: none;
}
.iconSvg {
	width: 33rpx;
	height: 33rpx;
}
.mx-auto {
	margin-left: auto;
	margin-right: auto;
}
.text-sm {
	font-size: 24rpx;
}
.w-2 {
	width: 8px;
}
.-ml-1 {
	margin-left: -4px;
}
.left-1 {
	left: 4px;
}
.direction {
	width: 40rpx;
}
</style>
