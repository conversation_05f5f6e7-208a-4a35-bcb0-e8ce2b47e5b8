<template>
	<view>
		<view class="page-empty" v-if="isPage">
			<img style="width: 320rpx" class="icon" :src="ossStaticResourceUrl + 'empty.svg'" alt="" />
			<br />
			<text class="tips" v-if="tips">{{ tips }}</text>
		</view>
		<view v-else class="empty" :class="size + ' ' + (noIcon ? 'noIcon' : '')">
			<div class="iconBox" v-if="!noIcon">
				<img class="icon" :src="ossStaticResourceUrl + 'empty.svg'" alt="" />
			</div>
			<br />
			<text class="tips" v-if="tips">{{ tips }}</text>
			<br />
			<text class="gray">{{ value || "暂无数据" }}</text>
		</view>
	</view>
</template>
<script>
import { toRefs } from "vue";
import { ossStaticResourceUrl } from "@/common/constant";

export default {
	name: "empty",
	props: {
		value: String,
		size: String,
		tips: String,
		noIcon: {
			type: Boolean,
			default: false,
		},
		isPage: {
			type: Boolean,
			default: false,
		},
	},
	setup(props) {
		const { value, size, noIcon } = toRefs(props);
		return { ossStaticResourceUrl };
	},
};
</script>
<style lang="scss" scoped>
.page-empty {
	background: #fff;
	padding-top: 236rpx;
	text-align: center;
	height: 100vh;
	image {
		width: 222rpx;
		height: 184rpx;
		margin-bottom: 48rpx;
	}
	text {
		color: #999999;
	}
}
.empty {
	display: block;
	width: 100%;
	font-size: 24rpx;
	text-align: center;
	.iconBox {
		height: 200rpx;
		// width: 40rpx;
		margin-bottom: 32rpx;
	}
	.icon {
		width: 100%;
		height: 100%;
	}
	&.large {
		padding: 60rpx 0;
	}
	&.huge {
		padding-top: 400rpx;
		.iconBox {
			height: 80px;
			margin-bottom: 8rpx;
		}
	}
	text {
		color: #999999;
	}
}
.noIcon {
	padding: 60rpx 0;
}
</style>
