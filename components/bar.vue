<template>
	<view>
		{{ pixelRatio }}
		<canvas
			:style="{
				width: width + 'rpx',
				height: height + 'rpx',
			}"
			:canvas-id="idx"
			:id="idx"
			type="2d"
			class="charts"
			@touchend="tap"
			:width="cWidth * pixelRatio"
			:height="cHeight * pixelRatio" />
	</view>
</template>

<script>
import { onBeforeMount, onMounted, reactive, toRefs, watch, computed } from "vue";
import uCharts from "@qiun/ucharts/u-charts.js";
import { transformToMod5, calculateMax, calculateMin } from "@/common/util";
import { barTitle } from "@/common/constant";
var uChartsInstance = {};
export default {
	props: {
		fromType: {
			type: String,
			default: () => "",
		},
		chartData: {
			type: Array,
			default: () => [],
		},
		dateTime: {
			type: Array,
			default: () => [],
		},
		width: {
			type: String,
			default: "640",
		},
		height: {
			type: String,
			default: "400",
		},
	},
	setup(props) {
		const { chartData, dateTime, fromType, width, height } = toRefs(props);
		const state = reactive({
			cWidth: 750,
			cHeight: 400,
			pixelRatio: 1,
		});
		const getServerData = () => {
			uni.showLoading();
			//模拟从服务器获取数据时的延时
			setTimeout(() => {
				//模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
				let res = {
					series: [
						{
							name: seriesName.value,
							data: chartData.value,
							textSize: 3 * state.pixelRatio,
							formatter: (value) => {
								return value.toFixed(0);
							},
						},
					],
				};
				drawCharts(idx.value, res);
				uni.hideLoading();
			}, 501);
		};
		onMounted(async () => {
			//这里的 750 对应 css .charts 的 width
			state.cWidth = uni.upx2px(width.value);
			//这里的 500 对应 css .charts 的 height
			state.cHeight = uni.upx2px(height.value);
			state.pixelRatio = uni.getSystemInfoSync().pixelRatio;
			// uni.showLoading();
			// setTimeout(() => {
			//   getServerData();
			// }, 501);
		});
		const idx = computed(() => {
			return "id" + parseInt(Math.random() * 1000000);
		});
		const seriesName = computed(() => {
			const name = barTitle.find((item) => {
				return item.value == fromType.value;
			});
			return name.name;
		});

		watch(fromType, (val) => {
			setTimeout(() => {
				getServerData();
			}, 501);
		});

		const drawCharts = (id, data) => {
			// let min = Math.min(...chartData.value).toFixed() * 2;
			// let max = Math.max(...chartData.value);
			let max = calculateMax(chartData.value);
			let min = calculateMin(chartData.value);
			const ctx = uni.createCanvasContext(id);
			uChartsInstance[id] = new uCharts({
				type: "column",
				context: ctx,
				width: state.cWidth * state.pixelRatio,
				height: state.cHeight * state.pixelRatio,
				categories: dateTime.value,
				series: data.series,
				animation: true,
				background: "#FFFFFF",
				color: ["#1ECC99", "#1ECC99"],
				padding: [15, 15, 0, 5],
				enableScroll: false,
				pixelRatio: state.pixelRatio,
				legend: {},
				xAxis: {
					disableGrid: true,
				},
				yAxis: {
					gridType: "dash",
					dashLength: "2",
					data: [
						{
							max: max,
							min: min,
						},
					],
				},
				extra: {
					column: {
						type: "group",
						width: 60 * state.pixelRatio,
						activeBgColor: "#000000",
						activeBgOpacity: 0.08,
						// linearType: "custom",
						// seriesGap: 5,
						// linearOpacity: 1,
						customColor: ["#1ECC99", "#1ECC99"],
						categoryGap: 10,
						barBorderCircle: false,
						// barBorderRadius: [0, 0, 0, 0],
					},
				},
			});
		};
		return {
			...toRefs(state),
			getServerData,
			drawCharts,
			idx,
			seriesName,
			barTitle,
		};
	},
};
</script>

<style scoped></style>
