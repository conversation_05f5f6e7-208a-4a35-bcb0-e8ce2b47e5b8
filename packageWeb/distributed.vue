<template>
  <view class="w-full h-full">
    <!-- #ifdef WEB -->
    <view id="tempDistributedChart" ref="tempDistributedChart"></view>
    <!-- #endif -->
    <!-- #ifdef MP-WEIXIN -->
    <l-echart ref="tempDistributedChart" @finished="onEChartReady"></l-echart>
    <!-- #endif -->
  </view>
</template>

<script setup>
import {
  reactive,
  toRefs,
  onMounted,
  computed,
  ref,
  getCurrentInstance,
  nextTick,
} from "vue";
//#ifdef WEB
import * as echarts from "echarts";
//#endif
//#ifdef MP-WEIXIN
import * as echarts from "echarts/dist/echarts.esm";
//#endif
const options = {
  gradientColor: ["#D4F6FB", "#A4DBE6", "#6FBECE", "#5FA7BA", "#204765"],
  tooltip: {
    show: false,
    position: "top",
    formatter: function (params) {
      return (
        params.dataIndex +
        1 +
        "号电芯" +
        params.seriesName +
        "<br />" +
        params.marker +
        "    " +
        params.value[2]
      );
    },
  },
  grid: {
    height: "88px",
    width: "96%",
    top: "10%",
    left: "2%",
  },
  xAxis: {
    type: "category",
    data: [],

    splitArea: {
      show: true,
    },
  },
  yAxis: {
    type: "category",
    data: [],
    splitArea: {
      show: true,
    },
    show: false,
  },
  visualMap: {
    min: 0,
    max: 10,
    calculable: true,
    orient: "horizontal",
    left: "center",
    bottom: "0",
    precision: "3",
    textStyle: {
      color: "rgba(34, 34, 34, 0.6)",
      // marginTop: "12px",
      // lineHeight: "12px",
    },
  },
  series: [
    {
      name: "Punch Card",
      type: "heatmap",
      data: [],
      label: {
        show: true,
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: "rgba(0, 0, 0, 0.5)",
        },
      },
    },
  ],
};
const tempDistributedChart = ref();
const transformData3 = (data, yCount) => {
  // 验证数据总量是否匹配
  const result = [];
  let xCount = Math.floor(data.length / yCount);
  console.log("xCount", xCount);
  if (data.length !== xCount * yCount) {
    throw new Error("数据总量与坐标数量不匹配");
  }

  // 按y轴数量分割数据
  for (let y = 0; y < yCount; y++) {
    // 计算当前y轴数据切片范围
    const start = y * xCount;
    const end = start + xCount;
    const yData = data.slice(start, end);
    // 生成坐标数据
    yData.forEach((value, x) => {
      result.push([x, y, value]);
    });
  }

  return result;
};
onMounted(() => {
  //
  const newVs = [
    3, 3, 4, 5, 1, 2, 3, 4, 3, 3.4, 3.2, 3, 5, 3, 4.2, 3, 4.1, 3, 5, 4,
  ];
  let min = Math.min(...newVs);
  let max = Math.max(...newVs);
  const res = transformData3(newVs, 2);
  options.visualMap.min = min;
  options.visualMap.max = max;
  options.series[0].data = res;
  options.yAxis.data = [];
  // #ifdef MP-WEIXIN
  // tempDistributedChart.value.init(echarts, (chart) => {
  //   chart.setOption(options);
  // });
  //#endif
  // #ifdef WEB
  const ElBox = document.getElementById("tempDistributedChart");
  console.log("[ 123 ] >", ElBox);
  nextTick(() => {
    const chartBox = echarts.init(ElBox);
    chartBox.setOption(options);
  });

  //#endif
});
// #ifdef MP-WEIXIN
function onEChartReady() {
  console.log("[ onEChartReady triggered ✅ ]");

  const newVs = [
    3, 3, 4, 5, 1, 2, 3, 4, 3, 3.4, 3.2, 3, 5, 3, 4.2, 3, 4.1, 3, 5, 4,
  ];
  let min = Math.min(...newVs);
  let max = Math.max(...newVs);
  const res = transformData3(newVs, 2);
  options.visualMap.min = min;
  options.visualMap.max = max;
  options.series[0].data = res;

  tempDistributedChart.value?.init(echarts, (chart) => {
    chart.setOption(options);
  });
}
//#endif
</script>

<style lang="scss" scoped>
#tempDistributedChart {
  width: 100%;
  height: 100%;
}
</style>