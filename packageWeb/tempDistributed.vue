<template>
  <view class="w-full h-full">
    <!-- #ifdef WEB -->
    <view
      id="distributedChart"
      ref="distributedChart"
      class="chart-container"
    ></view>
    <!-- #endif -->
    <!-- #ifdef MP-WEIXIN -->
    <l-echart
      ref="distributedChart"
      @finished="onEChartReady"
      class="chart-container"
    ></l-echart>
    <!-- #endif -->
  </view>
</template>

<script setup>
import { onMounted, ref, nextTick } from "vue";
//#ifdef WEB
import * as echarts from "echarts";
//#endif
//#ifdef MP-WEIXIN
// 微信小程序使用require引入echarts
const echarts = require("../../packageEcharts/lime-echart/static/echarts.min");
//#endif
// 定义响应式变量
const distributedChart = ref();
const chartInstance = ref(null);

// 创建图表配置
const createChartOptions = (data, min, max) => {
  return {
    gradientColor: ["#F3E49F", "#EAC391", "#C45054"],
    tooltip: {
      show: true,
      position: "top",
      formatter: function (params) {
        return (
          params.dataIndex +
          1 +
          "号传感器" +
          params.seriesName +
          "<br />" +
          params.marker +
          "    " +
          params.value[2] +
          "°C"
        );
      },
    },
    grid: {
      height: "44px",
      width: "96%",
      top: "10%",
      left: "2%",
    },
    xAxis: {
      type: "category",
      data: [],
      splitArea: {
        show: true,
      },
    },
    yAxis: {
      type: "category",
      data: [],
      splitArea: {
        show: true,
      },
      show: false,
    },
    visualMap: {
      min: min,
      max: max,
      calculable: true,
      orient: "horizontal",
      left: "center",
      bottom: "0",
      precision: 1,
      textStyle: {
        color: "rgba(34, 34, 34, 0.6)",
      },
    },
    series: [
      {
        name: "温度",
        type: "heatmap",
        data: data,
        label: {
          show: true,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };
};
const transformData3 = (data, yCount) => {
  // 验证数据总量是否匹配
  const result = [];
  let xCount = Math.floor(data.length / yCount);
  console.log("xCount", xCount);
  if (data.length !== xCount * yCount) {
    throw new Error("数据总量与坐标数量不匹配");
  }

  // 按y轴数量分割数据
  for (let y = 0; y < yCount; y++) {
    // 计算当前y轴数据切片范围
    const start = y * xCount;
    const end = start + xCount;
    const yData = data.slice(start, end);
    // 生成坐标数据
    yData.forEach((value, x) => {
      result.push([x, y, value]);
    });
  }

  return result;
};
// 初始化图表数据
const initChartData = () => {
  const newVs = [3, 3, 4, 5, 1, 2, 3, 4, 3, 3.4];
  let min = Math.min(...newVs);
  let max = Math.max(...newVs);
  const res = transformData3(newVs, 1);

  return {
    data: res,
    min: min,
    max: max,
  };
};

// H5端初始化图表
const initWebChart = () => {
  // #ifdef WEB
  const ElBox = document.getElementById("distributedChart");
  if (ElBox) {
    const chartData = initChartData();
    const options = createChartOptions(
      chartData.data,
      chartData.min,
      chartData.max
    );

    nextTick(() => {
      const chartBox = echarts.init(ElBox);
      chartBox.setOption(options);
      chartInstance.value = chartBox;
    });
  }
  //#endif
};

onMounted(() => {
  // #ifdef WEB
  initWebChart();
  //#endif
  // 微信小程序的初始化在onEChartReady中处理
});

// #ifdef MP-WEIXIN
function onEChartReady() {
  console.log("[ tempDistributed onEChartReady triggered ✅ ]");

  try {
    const chartData = initChartData();
    const options = createChartOptions(
      chartData.data,
      chartData.min,
      chartData.max
    );

    distributedChart.value?.init(echarts, (chart) => {
      if (chart) {
        chart.setOption(options);
        chartInstance.value = chart;
        console.log("微信小程序温度图表初始化成功");
      }
    });
  } catch (error) {
    console.error("微信小程序温度图表初始化失败:", error);
  }
}
//#endif

// 暴露组件方法供外部调用
defineExpose({
  updateChart: (newData) => {
    if (chartInstance.value) {
      const min = Math.min(...newData);
      const max = Math.max(...newData);
      const transformedData = transformData3(newData, 1);
      const options = createChartOptions(transformedData, min, max);
      chartInstance.value.setOption(options);
    }
  },
  getChartInstance: () => chartInstance.value,
});
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 100%;
  min-height: 100px;
}

#distributedChart {
  width: 100%;
  height: 100%;
}

/* 微信小程序特殊样式 */
/* #ifdef MP-WEIXIN */
.chart-container {
  background-color: #fff;
}
/* #endif */
</style>