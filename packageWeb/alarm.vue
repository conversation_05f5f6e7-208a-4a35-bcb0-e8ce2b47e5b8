<template>
  <div class="alarm">
    <view class="tabs">
      <view
        v-for="(item, index) in tabOptions"
        :key="item.id"
        class="tab"
        :class="[activeKey == item.id ? 'selected' : '']"
        @click="onChange(item.id, index)"
        :id="'tab' + item.id"
        >{{ item.label }}</view
      >
    </view>
    <scroll-view
      scroll-y
      @scrolltolower="scrolltolower"
      class="scorll"
      :class="{ 'scorll-operator'}"
    >
      <view v-for="item in list" :key="item.id" class="list">
        <AlarmItem :data="item" @refresh="refresh" />
      </view>
      <uni-load-more
        :status="loadMoreStatus"
        v-if="list.length > 0"
      ></uni-load-more>
      <view class="empty" v-if="list.length == 0">
        <image src="@/static/empty.png" />
        <view class="text">{{
           "当前系统安全运行，暂无任何异常"
        }}</view>
      </view>
    </scroll-view>
  </div>
</template>

<script setup>
import AlarmItem from "./alarmItem.vue";
import { onShow } from "@dcloudio/uni-app";
import { onPullDownRefresh, onLoad } from "@dcloudio/uni-app";
const tabOptions = ref([
  {
    label: "当前异常",
    id: "A",
  },
  {
    label: "历史异常",
    id: "B",
  },
]);

const loadMoreStatus = ref("noMore");
const onChange = (val, ind) => {
  console.log("val,ind", val, ind);
  activeKey.value = val;
};

const getActivePage = async () => {
	return service.deviceAlarmPage({ ...config, stage: alarmTypes[selectedAlarmType.value].value });
};

const listPage = async (boolean = false) => {
	uni.showLoading({
		title: "加载中",
	});
	// 获取数据
			const {
				data: {
					data: { records, total },
				},
			} = await getActivePage();
			uni.stopPullDownRefresh();
			uni.hideLoading();
			if (boolean) {
				list.value = records;
			} else {
				list.value = list.value.concat(records || []);
			}
			config.total = total;
			if (config.current * config.size >= total) {
				isLoading.value = true;
				loadMoreStatus.value = "noMore";
			} else {
				isLoading.value = false;
				loadMoreStatus.value = "more";
			}
		

};


const scrolltolower = () => {
	if (!isLoading.value) {
		console.log("[ 2 ] >", 2);
		config.current = config.current + 1;
		listPage();
	}
};
</script>

<style lang="scss" scoped>
.alarm {
  .tabs {
    height: 96rpx;
  }
  .scorll {
    //#ifdef MP-WEIXIN
    height: calc(100vh - 184rpx);
    //#endif
  }
  .scorll-operator {
    height: calc(100vh - 96rpx);
  }
  .empty {
    position: absolute;
    top: 20%;
    width: 100%;
    text-align: center;
    .text {
      height: 44rpx;
      font-size: 28rpx;
      font-family: AlibabaPuHuiTi_2_55_Regular;
      color: #000000;
      line-height: 44rpx;
    }
  }
}
</style>