<template>
  <view class="web">
    <view class="deviceInfo">
      <view class="car-img">
        <carImg :vehicleType="'forklift'" />
      </view>
      <view class="car-info">
        <view class="no">{{ "车辆001" }}</view>
        <view class="sn">设备编号：{{ "001" }}</view>
      </view>
    </view>
    <view class="content">
      <scroll-view
        class="scroll-view"
        :scroll-left="scrollLeft"
        :scroll-into-view="scrollIntoView"
      >
        <view class="tabs">
          <view
            v-for="(item, index) in tabOptions"
            :key="item.value"
            class="tab"
            :class="[activeKey == item.id ? 'selected' : '']"
            @click="onChange(item.id, index)"
            :id="'tab' + item.id"
            >{{ item.label }}</view
          >
        </view>
      </scroll-view>
      <view class="swiper-box">
        <swiper
          class="swiper"
          :duration="duration"
          :current-item-id="activeKey"
          @change="onSwiperChange"
          :style="{ height: swiperHeight + 'px' }"
        >
          <swiper-item item-id="A">
            <view class="sw-item" id="slide-A">
              <view class="real-info">
                <view class="device-state">
                  <div
                    class="flex items-center gap-x-1"
                    :style="{
                      color: getState(2, 'power').color,
                    }"
                  >
                    <i :class="['iconfont', getState(2, 'power').icon]"></i
                    ><span class="text-title dark:text-title-dark">{{
                      getState(2, "power").label
                        ? getState(2, "power").label
                        : ""
                    }}</span>
                  </div>
                </view>
                <view class="flex items-center">
                  <i :class="['iconfont', 'icon-ica-3']"></i>
                  <text class="ml-2">信号强度：{{ 21 }}</text>
                </view>
                <view class="flex items-center" style="color: #fd0b0b">
                  <i :class="['iconfont', 'icon-ica-3']"></i>
                  <text class="ml-2">风险 {{ 21 }}条</text>
                </view>
              </view>
              <view class="guage-chart">
                <!-- #ifdef WEB -->
                <gauge :value="29" />
                <!-- #endif -->
                <!-- #ifdef MP-WEIXIN -->
                <gauge1 :soc="20" />
                <!-- #endif -->
              </view>
              <view class="timeAndAddress flex items-center px-6">
                <view class="flex items-center">
                  <i :class="['iconfont', 'icon-ica-3']"></i>
                  <text class="ml-2">2026/05/11 05:31:11</text>
                </view>
                <view class="flex items-center ml-5">
                  <i :class="['iconfont', 'icon-ica-3']"></i>
                  <text class="ml-2">印度-新德里</text>
                </view>
              </view>
              <view class="statistic">
                <view class="statistic-content">
                  <view class="statistic-item">
                    <view class="num">{{ 3.14 }}</view>
                    <view class="name"> 总电压(V)</view>
                  </view>
                  <view class="statistic-item">
                    <view class="num">{{ 3.38 }}</view>
                    <view class="name"> 总电流(A) </view>
                  </view>
                  <view class="statistic-item">
                    <view class="num">99% </view>
                    <view class="name"> 电池健康度 </view>
                  </view>
                </view>
              </view>
              <view class="bg-line"></view>
              <view class="">
                <view class="">电芯单体详情</view>
                <!-- 电压 -->
                <view>
                  <view class="">电压热力分布</view>
                  <view class="distributed-chart">
                    <distributed />
                  </view>
                  <view class="distributed">
                    <view class="distributed-content">
                      <view class="distributed-item">
                        <view class="num">30V</view>
                        <view class="name">平均值</view>
                      </view>
                      <view class="distributed-item">
                        <view class="num">30V/#7</view>
                        <view class="name"> 最大值/电芯编号 </view>
                      </view>
                      <view class="distributed-item">
                        <view class="num">30V/#7</view>
                        <view class="name"> 最小值/电芯编号 </view>
                      </view>
                    </view>
                  </view>
                </view>
                <!-- 温度 -->
                <view>
                  <view class="">温度热力分布</view>
                  <view class="distributed-chart-temp">
                    <temp-distributed />
                  </view>
                  <view class="distributed">
                    <view class="distributed-content">
                      <view class="distributed-item">
                        <view class="num">30°C</view>
                        <view class="name">平均值</view>
                      </view>
                      <view class="distributed-item">
                        <view class="num">30°C/#7</view>
                        <view class="name"> 最大值/传感器编号 </view>
                      </view>
                      <view class="distributed-item">
                        <view class="num">30°C/#7</view>
                        <view class="name"> 最小值/传感器编号 </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </swiper-item>
          <swiper-item item-id="B">
            <view class="" id="slide-B">
              <view class="basic-info">
                <view class="info-box">
                  <view class="title b"> 服务商：{{ "万家乐公司" }} </view>
                  <view class="title b">
                    客户名称：{{ "江湾体育场都是客户公司" }}
                  </view>
                  <view class="title"> 累计循环次数：{{ 1 }} </view>
                  <view class="title"> 累计充电时间：{{ 1 }}h </view>
                  <view class="title"> 累计放电时间：{{ 1 }}h </view>
                  <view class="title"> 累计放电容量：{{ 1 }}Ah </view>
                </view>
                <view class="info-box">
                  <view class="title b"> 电芯信息 </view>
                  <view class="title"> 车辆类型：{{ 1 }} </view>
                  <view class="title"> 电芯类型：{{ 1 }} </view>
                  <view class="title"> 电芯封装：{{ 1 }} </view>
                  <view class="title"> 电芯个数：{{ 1 }} </view>
                  <view class="title"> 电池箱数：{{ 1 }} </view>
                  <view class="title"> 单体电压：{{ 1 }} V</view>
                  <view class="title"> 额定总压：{{ 1 }} V</view>
                  <view class="title"> 额定总流：{{ 1 }} A</view>
                  <view class="title"> 额定容量：{{ 1 }} Ah</view>
                  <view class="title"> 额定能量：{{ 1 }} kWh</view>
                </view>
                <view class="info-box">
                  <view class="title b"> 基础信息 </view>
                  <view class="title"> 设备编号：{{ 1 }} </view>
                  <view class="title"> 电池编号：{{ 1 }} </view>
                  <view class="title"> 设备型号：{{ 1 }} </view>
                  <view class="title"> HWID：{{ 1 }} </view>
                  <view class="title"> IMEI：{{ 1 }} </view>
                  <view class="title"> 软件版本：{{ 1 }} V</view>
                  <view class="title"> 出厂日期：{{ 1 }} V</view>
                  <view class="title"> 激活日期：{{ 1 }} A</view>
                  <view class="title"> 额定容量：{{ 1 }} Ah</view>
                  <view class="title"> 服务到期日期：{{ 1 }} kWh</view>
                </view>
              </view>
            </view>
          </swiper-item>
          <swiper-item item-id="C">
            <view class="sw-item" id="slide-C">
              <div class="div charge-title flex justify-between">
                <div class="div charge-title-l">
                  <span class="name">昨日充电量</span>
                  <div class="div flex items-center">
                    <span class="text-2.5xl font-bold ml-1 charge-num">
                      123
                    </span>
                    <span class="text-xs charge-unit"> MWh </span>
                  </div>
                </div>
                <div
                  class="div charge-title-r h-12 leading-12 rounded-r text-sm font-medium text-right"
                >
                  <span class="leading-tight text-left name">昨日放电量</span>
                  <div class="div flex items-center">
                    <span class="text-2.5xl font-bold ml-1 charge-num">
                      123
                    </span>
                    <span class="text-xs charge-unit">MWh</span>
                  </div>
                </div>
              </div>
              <view class="history">
                <view class="history-title flex">
                  <view class="history-title-l">
                    <view>
                      昨日充电量 {{ unitConversion(200)
                      }}{{ alternateUnits(200, 1000) ? "MWh" : "kWh" }}
                    </view>
                  </view>
                  <view class="history-title-r">
                    昨日放电量{{ unitConversion(200)
                    }}{{ alternateUnits(200, 1000) ? "MWh" : "kWh" }}
                  </view>
                </view>
              </view>
              <bar width="646" height="400" />
              <view class="run-list">
                <view>运行记录</view>
                <view class="run-title">
                  <view class="run-title-l">时间</view>
                  <view class="run-title-r">运行状态</view>
                  <view class="run-title-r">SOC</view>
                  <view class="run-title-r">重访代呢容量</view>
                </view>
                <view v-for="(item, index) in 10" :key="index">
                  <view class=""></view>
                </view>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import carImg from "./carImg.vue";
import gauge from "./gauge.vue";
import gauge1 from "./gauge1.vue";
import { getState } from "../common/setup";
import { unitConversion, alternateUnits } from "@/common/util";
import distributed from "./distributed";
import TempDistributed from "./tempDistributed";
import bar from "./newBar.vue";
const activeKey = ref("A");
const onChange = (val, ind) => {
  console.log("val,ind", val, ind);
  activeKey.value = val;
};
const tabOptions = ref([
  {
    label: "实时状态",
    id: "A",
  },
  {
    label: "基础信息",
    id: "B",
  },
  {
    label: "运行历史",
    id: "C",
  },
]);
const scrollLeft = ref(0);
const scrollIntoView = ref("tab");
// swiper
const duration = ref(400);
const swiperHeight = ref();
const updateSwiperHeight = () => {
  const id = `#slide-${activeKey.value}`;
  nextTick(() => {
    uni
      .createSelectorQuery()
      .select(id)
      .boundingClientRect((rect) => {
        if (rect) {
          swiperHeight.value = rect.height;
        }
      })
      .exec();
  });
};
const onSwiperChange = () => {
  updateSwiperHeight();
};
onMounted(() => {
  updateSwiperHeight(); // 初始高度
});
</script>

<style lang="scss" scoped>
.web {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.tabs {
  padding: 24rpx;
  white-space: nowrap;
  display: flex;
  line-height: 40rpx;
  justify-content: space-between;
  .tab {
    // display: inline;
    padding-bottom: 16rpx;
    color: $uni-secondary-color;
    // flex: 1;
    text-align: center;
    &.selected {
      color: $uni-main-color;
      position: relative;
      font-weight: bold;
      &.textColor {
        color: $uni-primary;
      }
      &::after {
        content: "";
        height: 8rpx;
        width: 48rpx;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        background-color: $uni-primary;
        border-radius: 4rpx;
      }
    }
  }
}
.deviceInfo {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.car-img {
  width: 262rpx;
  height: 156rpx;
}
.car-info {
  flex: 1;
  text-align-last: left;
  margin-left: 68rpx;
  .no {
    font-size: 28rpx;
    color: #6fbece;
    line-height: 48rpx;
  }
  .sn {
    font-size: 24rpx;
    color: rgba(34, 34, 34, 0.8);
    line-height: 34rpx;
  }
}
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100% - 156rpx);
}
.swiper-box {
  overflow-y: auto;
  flex: 1;
}
.swiper {
  width: 750rpx;
  height: 100vh;
  overflow-y: auto;
}
.sw-item {
  padding: 0 24rpx;
}
.guage-chart {
  width: 356rpx;
  height: 300rpx;
  margin: 0 auto;
}
.real-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
}

.statistic {
  padding-top: 16rpx;
  padding-bottom: 16rpx;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  margin-top: 16rpx;
}

.statistic-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;

  .statistic-item {
    text-align: center;
    position: relative;
    // padding-bottom: 16rpx;
    width: 33.333%;

    & + .statistic-item {
      border-left: 1px solid $uni-border-1;
      margin: 0;
    }

    .num {
      font-size: 32rpx;
      line-height: 48rpx;
      font-weight: bold;
      color: #1e312b;
    }

    .name {
      font-size: 24rpx;
      line-height: 40rpx;
      color: rgba(30, 49, 43, 0.6);
    }

    &::after {
      display: block;
      content: "";
      width: 96rpx;
      height: 8rpx;
      border-radius: 4rpx;
      background: transparent;
      position: absolute;
      left: 50%;
      margin-left: -48rpx;
      bottom: 0;
    }

    &.active {
      &::after {
        background: $uni-primary;
      }
    }
  }
}

.bg-line {
  width: 100%;
  height: 28rpx;
  background: rgba(34, 34, 34, 0.08);
}

.distributed {
  padding-top: 24rpx;
  padding-bottom: 24rpx;
  background: rgba(34, 34, 34, 0.08);
}

.distributed-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;

  .distributed-item {
    text-align: center;
    position: relative;
    width: 33.333%;

    & + .distributed-item {
      border-left: 1px solid rgba(0, 0, 0, 0.08);
      margin: 0;
    }

    .num {
      font-size: 32rpx;
      line-height: 48rpx;
      font-weight: bold;
      color: #1e312b;
    }

    .name {
      font-size: 24rpx;
      line-height: 40rpx;
      color: rgba(30, 49, 43, 0.6);
    }

    &::after {
      display: block;
      content: "";
      width: 96rpx;
      height: 8rpx;
      border-radius: 4rpx;
      background: transparent;
      position: absolute;
      left: 50%;
      margin-left: -48rpx;
      bottom: 0;
    }

    &.active {
      &::after {
        background: $uni-primary;
      }
    }
  }
}
.distributed-chart {
  width: 100%;
  height: 326rpx;
}
.distributed-chart-temp {
  width: 100%;
  height: 226rpx;
}
#slide-A {
  padding-bottom: 40rpx;
}
.basic-info {
  background: #f6f6f6;
  padding: 24rpx;
  margin-bottom: 24rpx;
}
.info-box {
  background: #fff;
  border-radius: 8rpx;
  padding: 24rpx 28rpx;
  & + .info-box {
    margin-top: 24rpx;
  }
}

.charge-title {
  position: relative;
  left: -6rpx;
  // margin-top: 16rpx;
  margin-bottom: 14rpx;
  display: flex;
  justify-content: space-between;
  color: #333;
  .name {
    font-size: 28rpx;
    line-height: 40rpx;
  }
  /* 基础文本样式 */
  .text-title {
    color: #333;
  }

  .dark .text-title-dark {
    color: #fff;
  }
}

.charge-title-l,
.charge-title-r {
  width: calc(50% - 38rpx);
  height: 96rpx;
  position: relative;
  display: flex;
  align-items: center;
  padding: 16rpx;
  border-radius: 8rpx 0 0 8rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.charge-title-l {
  background: rgba(51, 190, 79, 0.1);
  padding-left: 24rpx;
  justify-content: flex-start;
  border-radius: 8rpx 0 0 8rpx;

  /* 内部文本布局 */
  > .div:first-child {
    line-height: 48rpx;
    font-size: 32rpx;
  }

  /* 数字样式 */
  > .div:nth-child(2) {
    font-weight: bold;
  }

  /* 单位样式 */
  > .div:last-child {
    font-size: 24rpx;
    line-height: 40rpx;
    margin-left: 4rpx;
  }

  &::after {
    display: block;
    content: "";
    width: 0;
    height: 0;
    left: 100%;
    position: absolute;
    top: 0;
    border-top: 96rpx solid rgba(51, 190, 79, 0.1);
    border-right: 68rpx solid transparent;
  }
}

.charge-title-r {
  background: rgba(119, 155, 219, 0.1);
  padding-right: 24rpx;
  justify-content: flex-end;
  border-radius: 0 8rpx 8rpx 0;

  /* 内部文本布局 */
  > .div:first-child {
    line-height: 48rpx;
    font-size: 32rpx;
  }

  /* 数字样式 */
  > .div:nth-child(2) {
    font-size: 70rpx;
    line-height: 64rpx;
    font-weight: bold;
    margin-left: 36rpx;
  }

  /* 单位样式 */
  > .div:last-child {
    font-size: 24rpx;
    line-height: 40rpx;
    margin-left: 4rpx;
  }

  &::after {
    display: block;
    content: "";
    width: 0;
    height: 0;
    right: 100%;
    position: absolute;
    top: 0;
    border-bottom: 96rpx solid rgba(119, 155, 219, 0.1);
    border-left: 68rpx solid transparent;
  }
}

/* 保留原有的数字和单位样式类 */
.charge-num {
  letter-spacing: 0rpx;
  line-height: 1;
}

.charge-unit {
  line-height: 56rpx;
}
</style>