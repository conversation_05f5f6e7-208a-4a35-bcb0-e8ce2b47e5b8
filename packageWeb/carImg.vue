<template>
  <div class="w-full h-full">
    <image
      class="img"
      src="@/static/car/bus.png"
      v-if="vehicleType === 'bus'"
      alt=""
      srcset=""
    />
    <image
      class="img"
      src="@/static/car/forklift.png"
      v-else-if="vehicleType === 'forklift'"
      alt=""
      srcset=""
    />
    <image
      class="img"
      src="@/static/car/loader.png"
      v-else-if="vehicleType === 'loader'"
      alt=""
      srcset=""
    />
    <image
      class="img"
      src="@/static/car/tractor.png"
      v-else-if="vehicleType === 'tractor'"
      alt=""
      srcset=""
    />
    <!-- 叉车和升降车目前用同一张图 -->
    <image
      class="img"
      src="@/static/car/forklift.png"
      v-else-if="vehicleType === 'liftTruck'"
      alt=""
      srcset=""
    />
    <image class="img" src="@/static/car/tractor.png" v-else alt="" srcset="" />
  </div>
</template>

<script setup>
const props = defineProps({
  vehicleType: {
    type: String,
    default: "1",
  },
});
</script>

<style lang="less" scoped>
.img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>
