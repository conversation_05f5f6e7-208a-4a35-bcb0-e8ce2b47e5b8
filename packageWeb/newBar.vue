<template>
  <view class="chart-container">
    <!-- 顶部控制栏 -->
    <view class="chart-header">
      <!-- 左侧视图切换按钮 -->
      <view class="view-switch">
        <view
          class="switch-item"
          :class="{ active: currentView === 'capacity' }"
          @click="switchView('capacity')"
        >
          <text class="switch-text">充放电电量</text>
          <i class="iconfont icon-switch"></i>
        </view>
        <view
          class="switch-item"
          :class="{ active: currentView === 'duration' }"
          @click="switchView('duration')"
        >
          <text class="switch-text">充放电时长</text>
          <i class="iconfont icon-switch"></i>
        </view>
      </view>

      <!-- 右侧条件筛选下拉框 -->
      <view class="filter-dropdown">
        <picker
          :value="currentPeriodIndex"
          :range="periodOptions"
          range-key="label"
          @change="onPeriodChange"
        >
          <view class="picker-display">
            <text>{{ periodOptions[currentPeriodIndex].label }}</text>
            <i class="iconfont icon-arrow-down"></i>
          </view>
        </picker>
      </view>
    </view>

    <!-- 图表容器 -->
    <view class="chart-wrapper">
      <!-- #ifdef WEB -->
      <view id="chargeDischargeChart" class="chart-inner"></view>
      <!-- #endif -->
      <!-- #ifdef MP-WEIXIN -->
      <l-echart
        ref="chartRef"
        @finished="onEChartReady"
        class="chart-inner"
      ></l-echart>
      <!-- #endif -->
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from "vue";

// Props
const props = defineProps({
  width: {
    type: [String, Number],
    default: "100%",
  },
  height: {
    type: [String, Number],
    default: 190,
  },
});

// ECharts import
//#ifdef MP-WEIXIN
const echarts = require("./echarts.min.js");
//#endif

// 响应式数据
const chartInstance = ref(null);
const chartRef = ref();
const currentView = ref("capacity"); // 'capacity' | 'duration'
const currentPeriodIndex = ref(0);
const chartData = ref([]);
const loading = ref(false);

// 时间段选项配置
const periodOptions = ref([
  {
    label: "近七日",
    value: "week",
    params: {
      startDate: "2024-06-16",
      endDate: "2025-06-23",
      periodType: "day",
    },
  },
  {
    label: "近30天",
    value: "month",
    params: {
      startDate: "2024-05-23",
      endDate: "2025-06-23",
      periodType: "day",
    },
  },
  {
    label: "近一年",
    value: "year",
    params: {
      startMonth: "2024-06",
      endMonth: "2025-06",
      periodType: "month",
    },
  },
]);

// 格式化日期显示
const formatDate = (dateStr, periodType) => {
  if (!dateStr) return "";

  const date = new Date(dateStr);
  if (periodType === "day") {
    // MM/DD 格式
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${month}/${day}`;
  } else if (periodType === "month") {
    // MM 格式
    const month = String(date.getMonth() + 1).padStart(2, "0");
    return month;
  }
  return dateStr;
};

// 创建图表配置
const createChartOptions = () => {
  const currentPeriod = periodOptions.value[currentPeriodIndex.value];
  const isCapacityView = currentView.value === "capacity";

  // 处理数据
  const xAxisData = chartData.value.map((item) =>
    formatDate(item.date, currentPeriod.params.periodType)
  );

  const chargeData = chartData.value.map((item) =>
    isCapacityView ? item.chargeCap : item.chargeDur
  );

  const dischargeData = chartData.value.map((item) =>
    isCapacityView ? item.dischargeCap : item.dischargeDur
  );

  const unit = isCapacityView ? "kWh" : "h";
  const yAxisName = isCapacityView ? "电量(kWh)" : "时长(h)";

  return {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
      formatter: function (params) {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param) => {
          result += `${param.marker}${param.seriesName}: ${
            param.value || 0
          }${unit}<br/>`;
        });
        return result;
      },
    },
    legend: {
      data: ["充电", "放电"],
      top: 10,
      textStyle: {
        color: "#333",
        fontSize: 12,
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: xAxisData,
      axisLabel: {
        color: "#666",
        fontSize: 10,
      },
      axisLine: {
        lineStyle: {
          color: "#e0e0e0",
        },
      },
    },
    yAxis: {
      type: "value",
      name: yAxisName,
      nameTextStyle: {
        color: "#666",
        fontSize: 10,
      },
      axisLabel: {
        color: "#666",
        fontSize: 10,
      },
      axisLine: {
        lineStyle: {
          color: "#e0e0e0",
        },
      },
      splitLine: {
        lineStyle: {
          color: "#f0f0f0",
        },
      },
    },
    series: [
      {
        name: "充电",
        type: "bar",
        data: chargeData,
        itemStyle: {
          color: "#33BE4F",
        },
        barWidth: "30%",
      },
      {
        name: "放电",
        type: "bar",
        data: dischargeData,
        itemStyle: {
          color: "#779BDB",
        },
        barWidth: "30%",
      },
    ],
  };
};

// 模拟API调用
const fetchChartData = async () => {
  loading.value = true;
  try {
    const currentPeriod = periodOptions.value[currentPeriodIndex.value];
    console.log("请求参数:", currentPeriod.params);

    // 模拟接口调用
    await new Promise((resolve) => setTimeout(resolve, 500));

    // 模拟返回数据
    const mockData = generateMockData(currentPeriod.params.periodType);
    chartData.value = mockData;
    // 重新渲染图表
    updateChart();
  } catch (error) {
    console.error("获取图表数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 生成模拟数据
const generateMockData = (periodType) => {
  const data = [];
  const count = periodType === "month" ? 12 : 7;

  for (let i = 0; i < count; i++) {
    let date;
    if (periodType === "month") {
      date = `2024-${String(i + 1).padStart(2, "0")}-01`;
    } else {
      const d = new Date();
      d.setDate(d.getDate() - (count - 1 - i));
      date = d.toISOString().split("T")[0];
    }

    data.push({
      chargeCap: Math.random() * 100 + 20,
      chargeDur: Math.random() * 8 + 2,
      date: date,
      dischargeCap: Math.random() * 80 + 15,
      dischargeDur: Math.random() * 6 + 1,
    });
  }

  return data;
};

// 视图切换
const switchView = (view) => {
  if (currentView.value !== view) {
    currentView.value = view;
    updateChart();
  }
};

// 时间段切换
const onPeriodChange = (e) => {
  const newIndex = e.detail.value;
  if (currentPeriodIndex.value !== newIndex) {
    currentPeriodIndex.value = newIndex;
    fetchChartData(); // 切换时间段需要重新获取数据
  }
};

// 更新图表
const updateChart = () => {
  console.log("[ chartData.value ] >", chartData.value);
  if (chartInstance.value && chartData.value.length > 0) {
    const options = createChartOptions();
    chartInstance.value.setOption(options, true);
  }
};

// H5端初始化图表
const initWebChart = () => {
  // #ifdef WEB
  const ElBox = document.getElementById("chargeDischargeChart");
  if (ElBox) {
    nextTick(() => {
      const chart = echarts.init(ElBox);
      chartInstance.value = chart;

      // 设置图表尺寸
      chart.resize({
        width: props.width === "100%" ? ElBox.offsetWidth : props.width,
        height: props.height,
      });

      fetchChartData();
    });
  }
  //#endif
};

// #ifdef MP-WEIXIN
function onEChartReady() {
  console.log("[ newBar onEChartReady triggered ✅ ]");

  try {
    if (!echarts) {
      console.error("ECharts not loaded");
      return;
    }

    chartRef.value?.init(echarts, (chart) => {
      if (chart) {
        chartInstance.value = chart;
        console.log("微信小程序柱状图初始化成功");
        fetchChartData();
      }
    });
  } catch (error) {
    console.error("微信小程序柱状图初始化失败:", error);
  }
}
//#endif

// 监听视图变化
watch(currentView, () => {
  updateChart();
});

// 组件挂载
onMounted(() => {
  // #ifdef WEB
  initWebChart();
  //#endif
});

// 暴露方法供外部调用
defineExpose({
  refreshData: fetchChartData,
  switchView,
  getCurrentView: () => currentView.value,
  getCurrentPeriod: () => periodOptions.value[currentPeriodIndex.value],
});
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx 16rpx;
  border-bottom: 1px solid #f0f0f0;
}

.view-switch {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.switch-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    background: #e8f5e8;
    color: #33be4f;

    .switch-text {
      color: #33be4f;
      font-weight: 500;
    }

    .iconfont {
      color: #33be4f;
    }
  }

  .switch-text {
    font-size: 28rpx;
    color: #666;
    transition: color 0.3s ease;
  }

  .iconfont {
    font-size: 24rpx;
    color: #999;
    transition: color 0.3s ease;
  }
}

.filter-dropdown {
  .picker-display {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 12rpx 16rpx;
    background: #f8f9fa;
    border-radius: 8rpx;
    border: 1px solid #e0e0e0;
    min-width: 120rpx;
    cursor: pointer;

    text {
      font-size: 28rpx;
      color: #333;
    }

    .iconfont {
      font-size: 20rpx;
      color: #999;
      transition: transform 0.3s ease;
    }

    &:active {
      background: #f0f0f0;

      .iconfont {
        transform: rotate(180deg);
      }
    }
  }
}

.chart-wrapper {
  width: 100%;
  height: 380rpx; /* 190px * 2 */
  padding: 16rpx;
}

.chart-inner {
  width: 100%;
  height: 100%;
  background: #fff;
}

/* 微信小程序特殊样式 */
/* #ifdef MP-WEIXIN */
.chart-container {
  background-color: #fff;
}

.switch-item {
  /* 微信小程序中的点击效果 */
  &:active {
    background: #e8f5e8;
  }
}

.picker-display {
  /* 微信小程序中的选择器样式 */
  &:active {
    background: #f0f0f0;
  }
}
/* #endif */

/* H5特殊样式 */
/* #ifdef WEB */
.switch-item:hover {
  background: #f0f0f0;

  &.active:hover {
    background: #e8f5e8;
  }
}

.picker-display:hover {
  background: #f0f0f0;
  border-color: #d0d0d0;
}
/* #endif */
</style>
