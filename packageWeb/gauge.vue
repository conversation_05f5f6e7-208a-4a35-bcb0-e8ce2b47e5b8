<template>
  <view style="height: 300rpx" class="chart-wrapper">
    <!-- #ifdef WEB -->
    <view id="gaugeChart" class="chart-inner"></view>
    <!-- #endif -->
    <!-- #ifdef MP-WEIXIN -->
    <l-echart
      ref="chartRef"
      @finished="onEChartReady"
      class="chart-inner"
    ></l-echart>
    <!-- #endif -->
  </view>
</template>
<script setup>
import { ref, onMounted, watch, nextTick } from "vue";
//#ifdef WEB
import * as echarts from "echarts";
import "echarts-liquidfill";
//#endif
//#ifdef MP-WEIXIN
let echarts;
try {
  // 尝试从本地目录加载
  echarts = require("../uni_modules/lime-echart/static/echarts.min.js");
  // 确保在微信小程序环境中也加载 liquidfill 插件
  require("./echarts-liquidfill.min.js");
} catch (error) {
  // 尝试从uni_modules加载
  console.error("Failed to load echarts from local:", error);
  try {
    echarts = require("./echarts.min.js");
    // 尝试从当前目录加载 liquidfill 插件
    require("./echarts-liquidfill.min.js");
  } catch (error2) {
    console.error("Failed to load echarts from uni_modules:", error2);
  }
}
//#endif

const chartRef = ref();
const chartInstance = ref(null);

const initWebChart = () => {
  const ElBox = document.getElementById("gaugeChart");
  if (ElBox) {
    nextTick(() => {
      const chart = echarts.init(ElBox);
      chartInstance.value = chart;
      setOption();
    });
  }
};

const options = {
  // backgroundColor: '#050038',
  title: {
    textStyle: {
      fontWeight: "normal",
      fontSize: 25,
      color: "rgb(97, 142, 205)",
    },
  },
  grid: {
    top: "50%",
    left: "50%",
  },
  series: [
    {
      type: "gauge",
      radius: "90%", // 位置
      center: ["50%", "50%"],
      min: 0,
      max: 100,
      startAngle: 220,
      endAngle: -40,
      axisLine: {
        show: true,
        lineStyle: {
          // 轴线样式
          width: 6, // 宽度
          color: [
            [0.6, "#6FBECE"],
            [1, "rgba(233, 233, 233, 0.5)"],
          ], // 颜色
        },
      },
      pointer: {
        // 仪表盘指针
        show: false,
      },
      axisTick: {
        // 刻度
        show: false,
      },
      splitLine: {
        // 分割线
        show: false,
      },
      axisLabel: {
        // 刻度标签
        show: false,
      },
      zlevel: 2,
      data: [
        {
          value: 0,
          detail: {
            // 仪表盘详情
            show: true,
            fontSize: 12,
            color: "#fff",
            formatter: "剩余电量",
            offsetCenter: [0, "30%"],
          },
        },
      ],
    },
    {
      type: "liquidFill",
      radius: "70%",
      center: ["50%", "50%"],
      backgroundStyle: {
        color: "rgba(233, 233, 233, 0.5)",
        // opacity: 0.5,
        shadowColor: "rgba(233, 233, 233, 0.5)",
      },
      data: [],
      amplitude: 4, //水波振幅
      label: {
        //标签设置
        position: ["50%", "45%"],
        formatter: "", //显示文本,
        fontSize: 24, //文本字号,
        color: "#fff",
      },
      outline: {
        borderDistance: 3,
        itemStyle: {
          borderWidth: 0,
          shadowColor: "rgba(233, 233, 233, 0.5)",
        },
      },
      itemStyle: {
        color: "#6FBECE",
        shadowColor: "rgba(233, 233, 233, 0.5)",
      },
      zlevel: 1,
    },
  ],
};
const setOption = () => {
  const soc = 20;
  const value = soc / 100;
  if (soc < 10) {
    options.series[0].axisLine.lineStyle.color[0] = [value, "#FF4D4F"];
    options.series[1].itemStyle.color = "#FF4D4F";
  } else if (soc >= 10 && soc < 30) {
    options.series[0].axisLine.lineStyle.color[0] = [value, "#FD750B"];
    options.series[1].itemStyle.color = "#FD750B ";
  } else {
    options.series[0].axisLine.lineStyle.color[0] = [value, "#3EDACD"];
    options.series[1].itemStyle.color = "#3EDACD";
    // options.series[1].label;
  }
  if (!soc) {
    options.series[1].amplitude = 0;
    options.series[1].waveAnimation = 0;
  }
  options.series[1].data = [value, value];
  options.series[1].label.formatter = `${soc}%`;
  options.series[0].data[0].detail.formatter = "剩余电量";
  if (chartInstance.value) {
    chartInstance.value.setOption(options);
  }
};
// #ifdef MP-WEIXIN
function onEChartReady() {
  console.log("[ EchartWrapper onEChartReady triggered ✅ ]");
  try {
    // 确保echarts已加载
    if (!echarts) {
      console.error("ECharts not loaded");
      return;
    }
    chartRef.value?.init(echarts, (chart) => {
      if (chart) {
        chartInstance.value = chart;
        setOption();
        console.log("微信小程序图表初始化成功222");
      }
    });
  } catch (error) {
    console.error("微信小程序图表初始化失败:", error);
  }
}
//#endif
onMounted(() => {
  // #ifdef WEB
  initWebChart();
  //#endif
});
</script>

<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
.chart-inner {
  width: 100%;
  height: 100%;
}
</style>
