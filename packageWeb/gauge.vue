<template>
  <view style="height: 300rpx" class="i123">
    <!-- #ifdef MP-WEIXIN -->
    <l-echart ref="chart"></l-echart>
    <!-- #endif -->
    <!-- #ifdef WEB -->
    <view id="gaugeChart" ref="gaugeChart"></view>
    <!-- #endif -->
  </view>
</template>
<script setup>
// 或者按需引入
// import * as echarts from "@/uni_modules/lime-echart/static/echarts.min";
import { ref, onMounted, watch, nextTick } from "vue";
//#ifdef WEB
import * as echarts from "echarts";
import "echarts-liquidfill";
//#endif
//#ifdef MP-WEIXIN
const echarts = require("../packageEcharts/lime-echart/static/echarts.min");
//#endif
const props = defineProps({
  chartData: {
    type: Array,
    default: () => [],
  },
});

const chart = ref();
const gaugeChart = ref();
onMounted(() => {
  // console.log("[ chart.value ] >", chart.value);
  const options = {
    // backgroundColor: '#050038',
    title: {
      textStyle: {
        fontWeight: "normal",
        fontSize: 25,
        color: "rgb(97, 142, 205)",
      },
    },
    grid: {
      top: "50%",
      left: "50%",
    },
    series: [
      {
        type: "gauge",
        radius: "90%", // 位置
        center: ["50%", "50%"],
        min: 0,
        max: 100,
        startAngle: 220,
        endAngle: -40,
        axisLine: {
          show: true,
          lineStyle: {
            // 轴线样式
            width: 6, // 宽度
            color: [
              [0.6, "#6FBECE"],
              [1, "rgba(233, 233, 233, 0.5)"],
            ], // 颜色
          },
        },
        pointer: {
          // 仪表盘指针
          show: false,
        },
        axisTick: {
          // 刻度
          show: false,
        },
        splitLine: {
          // 分割线
          show: false,
        },
        axisLabel: {
          // 刻度标签
          show: false,
        },
        zlevel: 2,
        data: [
          {
            value: 0,
            detail: {
              // 仪表盘详情
              show: true,
              fontSize: 12,
              color: "#fff",
              formatter: "剩余电量",
              offsetCenter: [0, "30%"],
            },
          },
        ],
      },
      {
        type: "liquidFill",
        radius: "70%",
        center: ["50%", "50%"],
        backgroundStyle: {
          color: "rgba(233, 233, 233, 0.5)",
          // opacity: 0.5,
          shadowColor: "rgba(233, 233, 233, 0.5)",
        },
        data: [],
        amplitude: 4, //水波振幅
        label: {
          //标签设置
          position: ["50%", "45%"],
          formatter: "", //显示文本,
          fontSize: 24, //文本字号,
          color: "#fff",
        },
        outline: {
          borderDistance: 3,
          itemStyle: {
            borderWidth: 0,
            shadowColor: "rgba(233, 233, 233, 0.5)",
          },
        },
        itemStyle: {
          color: "#6FBECE",
          shadowColor: "rgba(233, 233, 233, 0.5)",
        },
        zlevel: 1,
      },
    ],
  };
  const soc = 20;
  const value = soc / 100;
  if (soc < 10) {
    options.series[0].axisLine.lineStyle.color[0] = [value, "#FF4D4F"];
    options.series[1].itemStyle.color = "#FF4D4F";
  } else if (soc >= 10 && soc < 30) {
    options.series[0].axisLine.lineStyle.color[0] = [value, "#FD750B"];
    options.series[1].itemStyle.color = "#FD750B ";
  } else {
    options.series[0].axisLine.lineStyle.color[0] = [value, "#3EDACD"];
    options.series[1].itemStyle.color = "#3EDACD";
    options.series[1].label;
  }
  if (!soc) {
    options.series[1].amplitude = 0;
    options.series[1].waveAnimation = 0;
  }
  options.series[1].data = [value, value];
  options.series[1].label.formatter = `${soc}%`;
  options.series[0].data[0].detail.formatter = "剩余电量";
  // #ifndef WEB
  chart.value.init(echarts, (chart) => {
    chart.setOption(options);
  });
  //#endif
  // #ifdef WEB
  const ElBox = document.getElementById("gaugeChart");
  console.log("[ 123 ] >", ElBox);
  nextTick(() => {
    const chartBox = echarts.init(ElBox);
    chartBox.setOption(options);
  });

  //#endif
});
// watch(
//   () => props.chartData,
//   (newVal) => {
//     if (newVal) {
//     }
//   }
// );
</script>

<style lang="scss" scoped>
#gaugeChart {
  width: 100%;
  height: 100%;
}
</style>
