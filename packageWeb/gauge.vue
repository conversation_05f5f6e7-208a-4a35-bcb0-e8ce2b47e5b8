<template>
  <view style="height: 300rpx" class="chart-wrapper">
    <!-- #ifdef WEB -->
    <view id="gaugeChart" class="chart-inner"></view>
    <!-- #endif -->
    <!-- #ifdef MP-WEIXIN -->
    <l-echart
      ref="chartRef"
      @finished="onEChartReady"
      class="chart-inner"
    ></l-echart>
    <!-- #endif -->
  </view>
</template>
<script setup>
import { ref, onMounted, nextTick } from "vue";
//#ifdef WEB
import * as echarts from "echarts";
import "echarts-liquidfill";
//#endif
//#ifdef MP-WEIXIN
let echarts;
try {
  // 先加载echarts
  echarts = require("./echarts.min.js");
  console.log("✅ ECharts loaded successfully");

  // 在微信小程序中，liquidfill插件可能不兼容
  // 暂时禁用liquidfill，只使用gauge
  console.log("🔧 微信小程序环境：使用gauge模式");
  modifyOptionsForGaugeOnly();
} catch (error) {
  console.error("❌ 1Failed to load echarts:", error);
  // 备用路径
  try {
    echarts = require("../uni_modules/lime-echart/static/echarts.min.js");
    console.log("✅ ECharts loaded from uni_modules");
  } catch (error2) {
    console.error("❌ 2Failed to load echarts from all paths:", error2);
  }
}
//#endif

const chartRef = ref();
const chartInstance = ref(null);
const liquidfillAvailable = ref(true);

// 当liquidfill插件不可用时，修改配置只使用gauge
const modifyOptionsForGaugeOnly = () => {
  liquidfillAvailable.value = false;
  console.log("🔧 Modifying options to use gauge only");
};

const initWebChart = () => {
  const ElBox = document.getElementById("gaugeChart");
  if (ElBox) {
    nextTick(() => {
      const chart = echarts.init(ElBox);
      chartInstance.value = chart;
      setOption();
    });
  }
};

// 创建基础的gauge配置
const createGaugeOptions = () => ({
  title: {
    textStyle: {
      fontWeight: "normal",
      fontSize: 25,
      color: "rgb(97, 142, 205)",
    },
  },
  grid: {
    top: "50%",
    left: "50%",
  },
  series: [
    {
      type: "gauge",
      radius: "90%",
      center: ["50%", "50%"],
      min: 0,
      max: 100,
      startAngle: 220,
      endAngle: -40,
      axisLine: {
        show: true,
        lineStyle: {
          width: 8,
          color: [
            [0.6, "#6FBECE"],
            [1, "rgba(233, 233, 233, 0.3)"],
          ],
        },
      },
      pointer: {
        show: true,
        length: "60%",
        width: 4,
        itemStyle: {
          color: "#6FBECE",
        },
      },
      axisTick: {
        show: true,
        length: 8,
        lineStyle: {
          color: "rgba(233, 233, 233, 0.6)",
          width: 1,
        },
      },
      splitLine: {
        show: true,
        length: 12,
        lineStyle: {
          color: "rgba(233, 233, 233, 0.8)",
          width: 2,
        },
      },
      axisLabel: {
        show: true,
        distance: 20,
        color: "rgba(34, 34, 34, 0.6)",
        fontSize: 10,
      },
      detail: {
        show: true,
        fontSize: 20,
        color: "#6FBECE",
        formatter: "{value}%",
        offsetCenter: [0, "70%"],
      },
      data: [
        {
          value: 0,
          name: "剩余电量",
        },
      ],
    },
  ],
});

// 创建带liquidfill的完整配置
const createLiquidFillOptions = () => ({
  title: {
    textStyle: {
      fontWeight: "normal",
      fontSize: 25,
      color: "rgb(97, 142, 205)",
    },
  },
  grid: {
    top: "50%",
    left: "50%",
  },
  series: [
    {
      type: "gauge",
      radius: "90%",
      center: ["50%", "50%"],
      min: 0,
      max: 100,
      startAngle: 220,
      endAngle: -40,
      axisLine: {
        show: true,
        lineStyle: {
          width: 6,
          color: [
            [0.6, "#6FBECE"],
            [1, "rgba(233, 233, 233, 0.5)"],
          ],
        },
      },
      pointer: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
      zlevel: 2,
      data: [
        {
          value: 0,
          detail: {
            show: true,
            fontSize: 12,
            color: "#fff",
            formatter: "剩余电量",
            offsetCenter: [0, "30%"],
          },
        },
      ],
    },
    {
      type: "liquidFill",
      radius: "70%",
      center: ["50%", "50%"],
      backgroundStyle: {
        color: "rgba(233, 233, 233, 0.5)",
        shadowColor: "rgba(233, 233, 233, 0.5)",
      },
      data: [],
      amplitude: 4,
      label: {
        position: ["50%", "45%"],
        formatter: "",
        fontSize: 24,
        color: "#fff",
      },
      outline: {
        borderDistance: 3,
        itemStyle: {
          borderWidth: 0,
          shadowColor: "rgba(233, 233, 233, 0.5)",
        },
      },
      itemStyle: {
        color: "#6FBECE",
        shadowColor: "rgba(233, 233, 233, 0.5)",
      },
      zlevel: 1,
    },
  ],
});

// 配置将在setOption函数中动态创建
const setOption = () => {
  const soc = 20;
  const value = soc / 100;

  // 根据liquidfill可用性选择配置
  const currentOptions = liquidfillAvailable.value
    ? createLiquidFillOptions()
    : createGaugeOptions();

  // 设置颜色
  let color = "#6FBECE";
  if (soc < 10) {
    color = "#FF4D4F";
  } else if (soc >= 10 && soc < 30) {
    color = "#FD750B";
  } else {
    color = "#3EDACD";
  }

  if (liquidfillAvailable.value) {
    // 使用完整的gauge + liquidfill配置
    currentOptions.series[0].axisLine.lineStyle.color[0] = [value, color];
    currentOptions.series[0].data[0].detail.formatter = "剩余电量";

    currentOptions.series[1].itemStyle.color = color;
    currentOptions.series[1].data = [value, value];
    currentOptions.series[1].label.formatter = `${soc}%`;

    if (!soc) {
      currentOptions.series[1].amplitude = 0;
      currentOptions.series[1].waveAnimation = 0;
    }
  } else {
    // 只使用gauge配置
    currentOptions.series[0].axisLine.lineStyle.color[0] = [value, color];
    currentOptions.series[0].data[0].value = soc;
    currentOptions.series[0].data[0].name = "剩余电量";
    currentOptions.series[0].pointer.itemStyle.color = color;
    currentOptions.series[0].detail.color = color;
    currentOptions.series[1].itemStyle.color = color;
    currentOptions.series[1].data = [value, value];
    currentOptions.series[1].label.formatter = `${soc}%`;
  }

  if (chartInstance.value) {
    chartInstance.value.setOption(currentOptions);
  }
};
// #ifdef MP-WEIXIN
function onEChartReady() {
  console.log("[ EchartWrapper onEChartReady triggered ✅ ]");
  try {
    // 确保echarts已加载
    if (!echarts) {
      console.error("ECharts not loaded");
      return;
    }
    chartRef.value?.init(echarts, (chart) => {
      if (chart) {
        chartInstance.value = chart;
        setOption();
        console.log("微信小程序图表初始化成功222");
      }
    });
  } catch (error) {
    console.error("微信小程序图表初始化失败:", error);
  }
}
//#endif
onMounted(() => {
  // #ifdef WEB
  initWebChart();
  //#endif
});
</script>

<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}
.chart-inner {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 9;
}
</style>
