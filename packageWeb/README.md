# Distributed 组件使用说明

## 概述

本目录包含两个热力图组件，用于显示电芯电压和温度的分布情况：

- `distributed.vue` - 电芯电压热力分布图
- `tempDistributed.vue` - 电芯温度热力分布图

这两个组件已经完善，支持在 **H5** 和 **微信小程序** 中正常渲染。

## 功能特性

### 跨平台支持
- ✅ H5 浏览器环境
- ✅ 微信小程序环境
- 🔄 自动适配不同平台的 ECharts 引入方式

### 图表功能
- 热力图可视化
- 自动计算数据范围
- 响应式布局
- 交互式提示框
- 数据更新支持

## 技术实现

### 架构设计
- 使用 `EchartWrapper.vue` 作为统一的图表包装器
- 通过条件编译处理不同平台的差异
- 统一的事件回调机制

### H5 环境
- 使用 `import * as echarts from "echarts"` 导入
- 通过 `document.getElementById` 获取 DOM 元素
- 直接使用 `echarts.init()` 初始化

### 微信小程序环境
- 使用 `require` 方式导入 echarts
- 使用 `l-echart` 组件包装
- 通过 `@finished` 事件处理初始化

## 使用方法

### 基本使用

```vue
<template>
  <view class="chart-wrapper">
    <!-- 电压分布图 -->
    <view class="voltage-chart">
      <distributed ref="voltageChart" />
    </view>
    
    <!-- 温度分布图 -->
    <view class="temp-chart">
      <temp-distributed ref="tempChart" />
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import distributed from './distributed.vue'
import tempDistributed from './tempDistributed.vue'

const voltageChart = ref()
const tempChart = ref()

// 更新图表数据
const updateCharts = () => {
  const voltageData = [3.1, 3.2, 3.0, 3.3, 3.1, 3.2, 3.4, 3.0, 3.2, 3.1]
  const tempData = [25, 26, 24, 27, 25, 26, 28, 24, 26, 25]
  
  voltageChart.value?.updateChart(voltageData)
  tempChart.value?.updateChart(tempData)
}
</script>
```

### 样式设置

```scss
.chart-wrapper {
  .voltage-chart {
    width: 100%;
    height: 326rpx;
    margin-bottom: 20rpx;
  }
  
  .temp-chart {
    width: 100%;
    height: 226rpx;
  }
}
```

## 组件 API

### Props
目前组件不接受外部 props，数据通过内部模拟生成。

### Methods
- `updateChart(newData: number[])` - 更新图表数据
- `getChartInstance()` - 获取 ECharts 实例

### Events
- `@finished` - 微信小程序环境下图表初始化完成事件

## 数据格式

### 电压数据
```javascript
// 20个电芯的电压值（单位：V）
const voltageData = [
  3, 3, 4, 5, 1, 2, 3, 4, 3, 3.4, 
  3.2, 3, 5, 3, 4.2, 3, 4.1, 3, 5, 4
]
```

### 温度数据
```javascript
// 10个传感器的温度值（单位：°C）
const tempData = [3, 3, 4, 5, 1, 2, 3, 4, 3, 3.4]
```

## 配置说明

### pages.json 配置
确保在 `pages.json` 中添加了 l-echart 组件的 easycom 配置：

```json
{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue",
      "^l-echart$": "@/packageEcharts/lime-echart/components/l-echart/l-echart.vue"
    }
  }
}
```

### 依赖要求
- ECharts 5.5.1+
- lime-echart 组件（已包含在 packageEcharts 目录）
- Vue 3 Composition API

## 注意事项

1. **微信小程序环境**：确保 `packageEcharts/lime-echart/static/echarts.min.js` 文件存在
2. **数据格式**：传入的数据数组长度必须能被行数整除
3. **样式适配**：组件会自动适配容器大小，建议设置明确的高度
4. **性能优化**：大数据量时建议使用数据采样或分页显示

## 故障排除

### 常见问题

1. **微信小程序中图表不显示**
   - 检查 echarts.min.js 文件路径是否正确
   - 确认 l-echart 组件是否正确导入
   - 确保 `uni_modules/lime-echart` 目录存在且包含完整文件

2. **H5 中图表不显示**
   - 检查容器是否有明确的高度设置
   - 确认 echarts 是否正确安装和导入

3. **数据更新不生效**
   - 确保调用 `updateChart` 方法时传入正确格式的数据
   - 检查组件 ref 是否正确绑定

4. **require 模块找不到错误**
   - 确认 `packageWeb/echarts.min.js` 文件存在（已复制到本地）
   - 确认 `uni_modules/lime-echart/static/echarts.min.js` 文件存在（备用路径）
   - 检查相对路径是否正确
   - 重新编译项目，清除缓存
   - 注意：require仅支持相对路径，不支持路径别名(@/)

## 更新日志

### v1.0.0 (2024-12-21)
- ✅ 完善 H5 和微信小程序双端支持
- ✅ 优化图表配置和数据处理逻辑
- ✅ 添加错误处理和调试信息
- ✅ 统一组件 API 和使用方式
