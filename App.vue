<script>
import store from "@/store/index.js";

import { onAuthAppBack } from "dingtalk-design-libs/biz/openAuthMiniApp";

export default {
  onLaunch: function () {},

  onShow: async function (options) {
    if (options.path == "packageWeb/index") {
      //
    } else {
      if (store.state.user.customerInfo.orgId) {
        //
      } else {
        // #ifndef H5
        await store.dispatch("user/getCustomerInfo");
        // #endif
      }
      const corpId = uni.getStorageSync("corpId");
      // 判断是否是钉钉小程序
      //#ifdef MP-DINGTALK
      if (corpId) {
        if (corpId != dd.corpId) {
          uni.removeStorageSync("accessToken");
          uni.navigateTo({
            url: "/packageDevice/login",
          });
        }
      }
      onAuthAppBack(options, (data) => {
        // 这里可以对返回数据做二次处理，之后需要把数据返回到page.onShow
        return data;
      });

      dd.getAuthCode({
        success: (res) => {},
      });
      //#endif
      //#ifdef MP-WEIXIN
      if (store.state.user.customerInfo.roles.includes("operation_staff")) {
        //
      } else {
        wx.setTabBarItem({
          index: 1,
          text: "风险",
          iconPath: "static/menu/alarm.png",
          selectedIconPath: "static/menu/alarm-active.png",
        });
      }

      //#endif
    }
  },
  onHide: function () {},
};
</script>

<style lang="scss">
/*每个页面公共css */
@import "@/scss/app.scss";
@import "@/scss/iconfont.scss";
@import "@/uni_modules/uni-scss/index.scss";
@import "@/scss/tailwind.scss";
</style>
