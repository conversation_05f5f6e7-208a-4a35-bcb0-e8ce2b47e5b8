<template>
  <view class="failure">
    <tabs-filter
      :selectedValue="activeId"
      :textColor="true"
      :options="tabs"
      @change="changeTabs"
      :scrollX="true"
    />
    <view class="list">
      <view class="failure-box" v-for="item in failureList" :key="item">
        <failure-item :data="item" @finish="finish" />
      </view>
      <empty
        v-if="failureList.length == 0"
        isPage
        :tips="
          activeId == 0
            ? '该设备没有待处理的故障'
            : activeId == 1
            ? '该设备没有已忽略的故障'
            : '该设备没有已处理的故障'
        "
      />
      <template v-if="failureList?.length >= 1">
        <uni-load-more
          v-show="isShowPage"
          :status="loadMoreStatus"
        ></uni-load-more>
      </template>
    </view>
  </view>
</template>

<script>
import FailureItem from "../components/failureItem.vue";
import { reactive, toRefs, onBeforeMount, ref, toRaw } from "vue";
import service from "../apiService/device";
import { onShow } from "@dcloudio/uni-app";
import { useLoadMore } from "@/common/setup";
import empty from "../components/empty.vue";
import tabsFilter from "@/components/tabsFilter";
export default {
  components: {
    FailureItem,
    empty,
    tabsFilter,
  },
  onPullDownRefresh: function () {
    this.pullDownRefresh();
  },
  setup() {
    onShow(() => {
      const pages = getCurrentPages();
      const options = pages[pages.length - 1].options;
      state.deviceSn = options.deviceSn;
      state.deviceName = options.deviceName;
    });
    const isShowPage = ref(false);
    const state = reactive({
      failureList: [],
      deviceSn: undefined,
      deviceName: undefined,
      tabs: [
        {
          label: "待处理",
          value: 0,
        },
        {
          label: "已忽略",
          value: 1,
        },
      ],
      activeId: 0,
    });
    const getList = async (param, isfresh = false) => {
      // isShowLoadMore.value = false;
      uni.showLoading({
        title: "加载中",
      });
      // state.listLoading = true;
      let result = await service.getFailureList({
        ...param,
        deviceSn: state.deviceSn,
        ignoreFlagList: [state.activeId],
      });
      if (isfresh) {
        state.failureList = result.data.data.records;
      } else {
        state.failureList = [...state.failureList, ...result.data.data.records];
      }
      setTotalPage(result.data.data.pages);
      // isShowLoadMore.value = true;
      // state.listLoading = false;
      uni.hideLoading();
    };
    const { loadMoreStatus, setTotalPage, page } = useLoadMore(5, getList);
    const pullDownRefresh = async () => {
      page.current = 1;
      refreshList();
      uni.stopPullDownRefresh(); //刷新完成后停止下拉刷新动效
    };
    const refreshList = async () => {
      let param = { ...toRaw(page) };
      await getList(param, true);
    };
    const finish = async () => {
      refreshList();
    };
    onBeforeMount(async () => {
      await getList();
    });
    const changeTabs = (item, index) => {
      state.activeId = item;
      refreshList();
    };
    return {
      ...toRefs(state),
      getList,
      refreshList,
      loadMoreStatus,
      // isShowLoadMore,
      pullDownRefresh,
      finish,
      changeTabs,
    };
  },
};
</script>

<style lang="scss" scoped>
.failure {
  padding-top: 12rpx;
}
.failure-box {
  & + .failure-box {
    margin-top: 24rpx;
  }
}
</style>
