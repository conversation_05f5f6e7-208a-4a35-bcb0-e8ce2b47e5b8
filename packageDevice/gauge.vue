<template>
  <view class="content">
    <!-- <canvas :canvas-id="idx" :id="idx" ref="canvasRef" type="2d" class="charts" :width="cWidth * pixelRatio" :height="cHeight * pixelRatio" /> -->

    <!-- #ifdef MP-DINGTALK -->
    <canvas
      canvas-id="id996rnm"
      id="id996rnm"
      :style="{ width: cWidth + 'px', height: cHeight + 'px' }"
      :width="cWidth * pixelRatio"
      :height="cHeight * pixelRatio"
    />
    <!-- #endif -->
    <!-- #ifdef MP-WEIXIN -->
    <canvas canvas-id="id996rnm" id="id996rnm" type="2d" class="charts" />
    <!-- #endif -->
  </view>
</template>

<script>
import {
  onBeforeMount,
  onMounted,
  reactive,
  toRefs,
  watch,
  computed,
  nextTick,
  ref,
  getCurrentInstance,
} from "vue";
import uCharts from "@qiun/ucharts/u-charts.js";
var uChartsInstance = {};
export default {
  props: {
    soc: {
      type: Number,
      default: () => undefined,
    },
  },
  setup(props) {
    const { soc } = toRefs(props);
    const state = reactive({
      cWidth: 750,
      cHeight: 508,
      pixelRatio: 1,
      chartColor: "#1ECC99",
    });
    const canvasRef = ref(null);
    const canvasContext = ref(null);
    const idx = computed(() => {
      return "id996rnm";
    });
    onMounted(async () => {
      //这里的 750 对应 css .charts 的 width
      state.cWidth = uni.upx2px(350);
      //这里的 500 对应 css .charts 的 height
      state.cHeight = uni.upx2px(320);
      state.pixelRatio = uni.getSystemInfoSync().pixelRatio;
    });
    watch(
      soc,
      (val) => {
        if (val === undefined || val === null) return;
        nextTick(() => {
          getServerData();
          state.chartColor =
            Number(soc.value) > 30
              ? "#3EDACD"
              : Number(soc.value) > 10
              ? "#FD750B"
              : "#FF4D4F";
        });
      },
      { immediate: true }
    );
    const getServerData = () => {
      //
      setTimeout(() => {
        //
        let res = {
          categories: [
            { value: 0.4, color: "#ff0000" },
            { value: 0.8, color: "#ff0000" },
            { value: 1, color: "#ff0000" },
          ],
          series: [
            {
              name: "完成率",
              data: (soc.value || 0) / 100,
            },
          ],
        };
        drawCharts(idx.value, res);
      }, 500);
    };
    const instance = getCurrentInstance();
    const drawCharts = (id, data) => {
      nextTick(() => {
        //#ifdef MP-DINGTALK
        const ctx = uni.createCanvasContext(id);
        uChartsInstance[id] = new uCharts({
          type: "gauge",
          context: ctx,
          width: state.cWidth * state.pixelRatio,
          height: state.cHeight * state.pixelRatio,
          categories: data.categories,
          series: data.series,
          animation: true,
          background: "rgba(0,0,0,0.5)",
          color: [
            state.chartColor,
            state.chartColor,
            state.chartColor,
            state.chartColor,
            state.chartColor,
            state.chartColor,
            state.chartColor,
            state.chartColor,
            state.chartColor,
          ],
          padding: [0, 0, 0, 0],
          title: {
            name: (soc.value || 0) + "%",
            fontSize: 26 * state.pixelRatio,
            color: "#111111",
            offsetY: 0,
          },
          subtitle: {
            name: "剩余电量",
            fontSize: 12 * state.pixelRatio,
            color: "#111111",
            offsetY: 0,
          },
          extra: {
            gauge: {
              type: "progress",
              width: 13 * state.pixelRatio,
              labelColor: state.chartColor,
              startAngle: 0.75,
              endAngle: 0.25,
              startNumber: 0,
              endNumber: 100,
              labelFormat: "",
              splitLine: {
                fixRadius: 10, // 0
                splitNumber: 10, // 10
                width: 10, //0
                color: state.chartColor,
                childNumber: -1,
                childWidth: 10, // 0
              },
              pointer: {
                width: 24,
                color: "auto",
              },
            },
          },
        });
        //#endif
        //#ifdef MP-WEIXIN
        // const query = uni.createSelectorQuery().select("#id996rnm");
        const query = uni
          .createSelectorQuery()
          .in(instance)
          .select("#id996rnm");
        query.fields({ node: true, size: true }).exec((res) => {
          if (res[0]) {
            const canvas = res[0].node;
            const ctx = canvas.getContext("2d");
            canvas.width = res[0].width * state.pixelRatio;
            canvas.height = res[0].height * state.pixelRatio;
            uChartsInstance[id] = new uCharts({
              type: "gauge",
              context: ctx,
              width: state.cWidth * state.pixelRatio,
              height: state.cHeight * state.pixelRatio,
              categories: data.categories,
              series: data.series,
              animation: true,
              background: "rgba(0,0,0,0.5)",
              color: [
                state.chartColor,
                state.chartColor,
                state.chartColor,
                state.chartColor,
                state.chartColor,
                state.chartColor,
                state.chartColor,
                state.chartColor,
                state.chartColor,
              ],
              padding: [0, 0, 0, 0],
              title: {
                name: (soc.value || 0) + "%",
                fontSize: 26 * state.pixelRatio,
                color: "#111111",
                offsetY: 0,
              },
              subtitle: {
                name: "剩余电量",
                fontSize: 12 * state.pixelRatio,
                color: "#111111",
                offsetY: 0,
              },
              extra: {
                gauge: {
                  type: "progress",
                  width: 13 * state.pixelRatio,
                  labelColor: state.chartColor,
                  startAngle: 0.75,
                  endAngle: 0.25,
                  startNumber: 0,
                  endNumber: 100,
                  labelFormat: "",
                  splitLine: {
                    fixRadius: 10, // 0
                    splitNumber: 10, // 10
                    width: 10, //0
                    color: state.chartColor,
                    childNumber: -1,
                    childWidth: 10, // 0
                  },
                  pointer: {
                    width: 24,
                    color: "auto",
                  },
                },
              },
            });
          }
        });
        //#endif
      });
    };
    return {
      ...toRefs(state),
      getServerData,
      drawCharts,
      idx,
      canvasRef,
      canvasContext,
    };
  },
};
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.charts {
  width: 350rpx;
  height: 320rpx;
}
</style>
