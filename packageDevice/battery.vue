<template>
	<view class="flex dc">
		<view class="left">
			<view class="outer">
				<view class="insider" :style="insiderStyle" v-if="failed == 0"></view>
				<view class="error" v-else>
					<i class="iconfont icon-a-ica-dianchi-guzhangbeifen16"></i>
				</view>
			</view>
			<view class="num">
				<view class="tit">soc</view>
				<view class="num" :style="{ color: socColor }">{{ (Number(soc).toFixed(0) || 0) + "%" }}</view>
			</view>
		</view>
		<view class="right">{{ failed == 0 ? "正常" : "故障" }}</view>
	</view>
</template>

<script>
import { reactive, toRefs, computed } from "vue";
export default {
	props: {
		soc: {
			type: Number,
			default: 0,
		},
		failed: {
			type: Number,
			default: 0,
		},
	},
	setup(props) {
		const { soc, failed } = toRefs(props);
		const state = reactive({});
		const insiderStyle = computed(() => {
			return {
				width: Number(soc.value) + "%",
				background:
					Number(soc.value) > 30
						? "linear-gradient(180deg, #1ECC99 0%, #1ECC99 100%)"
						: Number(soc.value) > 15
						? "linear-gradient(180deg, #FD750B 0%, #FD750B 100%)"
						: "linear-gradient(180deg, #FD0B0B 0%, #FD0B0B 100%)",
			};
		});
		const socColor = computed(() => {
			return Number(soc.value) > 30 ? "#1ECC99" : Number(soc.value) > 15 ? "#FD750B" : "#FD0B0B";
		});
		return {
			...toRefs(state),
			insiderStyle,
			socColor,
		};
	},
};
</script>

<style lang="scss" scoped>
.dc {
	display: flex;
	justify-content: space-between;
	align-items: center;
	.left {
		flex: 1;
		display: flex;
		.outer {
			width: 232rpx;
			height: 88rpx;
			border-radius: 16rpx;
			border: 4rpx solid #2b2a2f;
			position: relative;
			padding: 4rpx;
			margin-right: 32rpx;
			&::after {
				display: block;
				content: "";
				width: 12rpx;
				height: 30rpx;
				background: #2b2a2f;
				position: absolute;
				border-radius: 2rpx;
				left: 100%;
				top: 50%;
				margin-top: -15rpx;
			}
			.insider {
				width: 216rpx;
				height: 72rpx;
				background: linear-gradient(90deg, #81ffda 0%, $uni-primary 100%);
				border-radius: 12rpx;
			}
			.error {
				width: 100%;
				height: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				left: 0;
				top: 0;
				.iconfont {
					font-size: 44rpx;
					color: $uni-error;
				}
			}
		}
		.tit {
			font-size: 28rpx;
			line-height: 40rpx;
			color: $uni-main-color;
		}
		.num {
			font-size: 28rpx;
			line-height: 40rpx;
			color: rgba(30, 204, 153, 1);
		}
	}
	.right {
		width: 150rpx;
		height: 80rpx;
		background: #f6f7f7;
		border-radius: 16rpx;
		font-size: 32rpx;
		line-height: 80rpx;
		text-align: center;
		color: $uni-main-color;
	}
}
</style>
