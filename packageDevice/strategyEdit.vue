<template>
	<view>
		<view class="tips" v-if="!detail.deviceOnlineStatus"> *当前储能设备处于离线状态，暂时不能进行电池策略编辑 </view>
		<div class="content">
			<div class="times">
				<view class="name"> {{ detail.workProcessName }}</view>
				<view>
					<div class="tabs">
						<div class="tab" v-for="item in tabs" :key="item.value" :class="activeTab == item.value ? 'tabs' + activeTab : ''" @click="changeTab(item.value)">
							{{ item.label }}
						</div>
					</div>
					<div class="date-list">
						<div v-for="time in detail.deviceHourWorkProcessList" :key="time.time" :class="['time-slot', 'time-slot' + time.batteryWorkStatus]" @click="handleTimeSlotClick(time)">
							<span>{{ time.hour }}</span>
							<span>{{ getSegmentType(time.segmentType).label }}</span>
						</div>
					</div>
					<div></div>
				</view>
			</div>
			<div class="forms">
				<div class="flex form-items">
					<div class="label">最大充电功率</div>
					<div style="width: 120px" class="form-input">
						<uni-easyinput
							v-model="chargePower"
							placeholderStyle="font-size:28rpx;line-height: 44rpx;"
							:maxlength="100"
							type="number"
							:max="100"
							:inputBorder="false"
							:clearable="false"
							@change="changeChargeValue"
							:disabled="!detail.deviceOnlineStatus"></uni-easyinput>
						KW
					</div>
				</div>
				<div class="flex form-items">
					<div class="label">最大放电功率</div>
					<div style="width: 120px" class="form-input">
						<uni-easyinput
							v-model="dischargePower"
							placeholderStyle="font-size:28rpx;line-height: 44rpx;"
							:maxlength="100"
							type="number"
							:max="100"
							:inputBorder="false"
							:clearable="false"
							@change="changeDisChargeValue"
							:disabled="!detail.deviceOnlineStatus"></uni-easyinput
						>KW
					</div>
				</div>
			</div>
			<div>
				<button class="primary submitBtn large" @click="onSubmit" :disabled="!detail.deviceOnlineStatus">完成设置</button>
				<view class="useRecommend" @click="useRecommend">使用推荐策略</view>
			</div>
		</div>
	</view>
</template>

<script>
import service from "../apiService/device";
import { onMounted, reactive, toRefs, watch } from "vue";
import { onShow } from "@dcloudio/uni-app";
import { segmentType } from "@/common/constant.js";
import { getTimeSegments } from "@/common/util";
export default {
	components: {},
	setup() {
		const state = reactive({
			detail: {
				deviceOnlineStatus: 1,
				deviceHourWorkProcessList: new Array(24).fill({
					hour: "",
					segmentType: 3,
					batteryWorkStatus: 0,
				}),
			},
			tabs: [
				{
					label: "选择充电时段",
					value: 1,
				},
				{
					label: "选择放电时段",
					value: 2,
				},
			],
			activeTab: 1,
			chargePower: 50,
			dischargePower: 50,
		});
		onMounted(() => {});
		onShow(async () => {
			const pages = getCurrentPages();
			const options = pages[pages.length - 1].options;
			state.deviceSn = options.deviceSn;
			state.workProcessId = options.workProcessId;
			getDetail();
		});
		const getDetail = async () => {
			uni.showLoading();
			//
			let result = await service.getBatteryWorkProcessDetail(state.deviceSn, state.workProcessId);
			state.detail = result.data.data;
			state.chargePower = result.data.data.chargePower;
			state.dischargePower = result.data.data.dischargePower;
			uni.hideLoading();
		};
		const changeTab = (value) => {
			// 如果正常状态 则继续
			if (!!state.detail.deviceOnlineStatus) {
				state.activeTab = value;
			}
		};
		const handleTimeSlotClick = (time) => {
			if (!state.detail.deviceOnlineStatus) {
				// 储能设备不在线，不给操作
				return;
			}
			time.batteryWorkStatus = state.activeTab === 1 ? (time.batteryWorkStatus === 1 ? 0 : 1) : time.batteryWorkStatus === 2 ? 0 : 2;
		};
		const onSubmit = () => {
			// uni.showModal({
			//   title: "",
			//   content: "确认变更该策略并下发设备？",
			//   confirmText: "确定",
			//   cancelText: "取消",
			//   success: async function (res) {
			//     if (res.confirm) {
			//       let newTimeSegments = getTimeSegments(
			//         state.detail.deviceHourWorkProcessList
			//       );
			//       let param = {
			//         deviceSn: state.deviceSn,
			//         workProcessId: state.workProcessId,
			//         segmentWorkProcess: newTimeSegments,
			//         chargePower: state.chargePower,
			//         dischargePower: state.dischargePower,
			//       };
			//       await service.updateBatteryWorkProcess(param);
			//       uni.showToast({
			//         title: "操作成功",
			//         icon: "success",
			//       });
			//     }
			//   },
			// });
		};
		const getSegmentType = (val) => {
			return segmentType.filter((item) => item.value == val)[0];
		};
		const useRecommend = async () => {
			if (!state.detail.deviceOnlineStatus) {
				// 储能设备不在线，不给操作
				return;
			}
			let res = await service.getRecommendBatteryWorkProcess(state.deviceSn, state.workProcessId);
			state.detail.deviceHourWorkProcessList = res.data.data;
		};
		const changeChargeValue = (value) => {
			state.chargePower = +value > 100 ? 100 : value < 0 ? 0 : value;
		};
		const changeDisChargeValue = (value) => {
			state.dischargePower = +value > 100 ? 100 : value < 0 ? 0 : value;
		};
		return {
			...toRefs(state),
			changeTab,
			handleTimeSlotClick,
			segmentType,
			getSegmentType,
			onSubmit,
			useRecommend,
			changeChargeValue,
			changeDisChargeValue,
		};
	},
};
</script>

<style lang="scss" scoped>
.content {
	padding: 28rpx;
}
.name {
	font-size: 28rpx;
	height: 44rpx;
	line-height: 44rpx;
	margin-bottom: 26rpx;
}
.times {
	padding: 28rpx;
	background: #fff;
	border-radius: 8rpx;
}

.tabs {
	display: flex;
	row-gap: 24rpx;
	column-gap: 24rpx;
	margin-bottom: 32rpx;
}
.tab {
	padding: 0 20rpx;
	border: 1px solid #d9d9d9;
	font-size: 24rpx;
	line-height: 64rpx;
	border-radius: 8rpx;
	color: rgba($color: #222222, $alpha: 0.6);
	border-color: rgba($color: #222222, $alpha: 0.08);
	background-color: rgba($color: #959ec3, $alpha: 0.1);
	&.tabs1 {
		color: #1ecc99;
		border-color: #1ecc99;
		background: rgba($color: #1ecc99, $alpha: 0.2);
	}
	&.tabs2 {
		color: #fd750b;
		border-color: #fd750b;
		background: rgba($color: #fd750b, $alpha: 0.2);
	}
}
.date-list {
	display: flex;
	flex-wrap: wrap;
	row-gap: 20rpx;
	column-gap: 14rpx;
}

.time-slot {
	width: 116rpx;
	height: 116rpx;
	border: 1px solid #ccc;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	background: white;
	border-radius: 8rpx;
	font-size: 24rpx;
	line-height: 28rpx;
	color: rgba($color: #222222, $alpha: 0.6);
	border-color: rgba($color: #222222, $alpha: 0.08);
	background-color: rgba($color: #fff, $alpha: 0.1);
	&.time-slot1 {
		color: #1ecc99;
		border-color: #1ecc99;
		background-color: rgba($color: #1ecc99, $alpha: 0.2);
	}
	&.time-slot2 {
		color: #fd750b;
		border-color: #fd750b;
		background-color: rgba($color: #fd750b, $alpha: 0.2);
	}
}
.forms {
	background: #fff;
	padding: 0 32rpx;
	border-radius: 8rpx;
	margin-top: 44rpx;
	font-size: 28rpx;
	line-height: 44rpx;
}
.form-items {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx 0 16rpx;
	& + .form-items {
		border-top: 1px solid rgba(30, 49, 43, 0.08);
	}
	.form-input {
		display: flex;
		align-items: center;
		width: 120px;
		text-align: right;
		justify-content: flex-end;
	}
	:deep(.uni-easyinput__content) {
		background: transparent !important;
	}
	:deep(.uni-easyinput__content-input) {
		height: 44rpx;
		line-height: 44rpx;
		border: none;
		text-align: right;
		column-gap: 16rpx;
		background: transparent;
	}
	:deep(.uni-numbox__value) {
		background: transparent !important;
		border: transparent !important;
	}
	:deep(.uni-numbox-btns) {
		display: none !important;
	}
}
.submitBtn {
	width: 320rpx;
	margin: 64rpx auto 32rpx;
}
.useRecommend {
	font-size: 28rpx;
	line-height: 44rpx;
	color: $uni-primary;
	text-align: center;
}
.tips {
	width: 100%;
	font-size: 24rpx;
	line-height: 72rpx;
	background: rgba(253, 11, 11, 0.2);
	color: #fd0b0b;
	padding: 0 16rpx;
}
</style>
