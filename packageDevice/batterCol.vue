<template>
  <view class="battery" @click="showSortNo">
    <!-- <view class="num">{{ info.sortNo }}#</view> -->
    <!-- <view class="battery-info">
      <view class="box">
        <view class="percent" v-if="!info.malfunction"
          >{{ info.soc.toFixed(0) }}%</view
        >
        <view class="percent fault" v-else
          ><i class="iconfont icon-a-ica-dianchi-guzhangbeifen16 error"></i
        ></view>
        <view
          class="power"
          :style="insiderStyle"
          v-if="!info.malfunction"
        ></view>
      </view>
    </view>
    <view class="text">{{
      info.malfunction ? "故障" : info.voltage + "v/" + info.temperature + "°C"
    }}</view> -->
    <view class="battery-content">
      <view class="battery-content-title">{{ info.soc }}%</view>
      <view class="battery-bt-content">
        <view class="voltage">{{ info.voltage }}v</view>
        <view class="temperature">{{ info.temperature }}°C</view>
      </view>
    </view>
    <view class="power" :style="insiderStyle"></view>
    <view class="click-show" v-if="index == clickIndex">
      <view class="box">#{{ info.sortNo }}</view>
    </view>
  </view>
</template>

<script>
import { computed, toRefs,ref } from "vue";
export default {
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
    index:{
      type:Number
    },
    clickIndex:{
      type:Number
    }
  },
  setup(props) {
    const { info, fault ,index,clickIndex} = toRefs(props);
    const insiderStyle = computed(() => {
      return {
        height: Number(info.value.soc) + "%",
        background:
          Number(info.value.soc) > 30
            ? "linear-gradient(180deg, #81FFDA 0%, #1ECC99 100%)"
            : Number(info.value.soc) > 10
            ? "linear-gradient(180deg, #f5bb8f 0%, #FD750B 100%)"
            : "linear-gradient(180deg, #ff9090 0%, #FD0B0B 100%)",
      };
    });

    return {
      insiderStyle,
      index,
      clickIndex
    };
  },
};
</script>

<style lang="scss" scoped>
.battery {
  // padding: 12rpx 14rpx;
  height: 134rpx;
  text-align: center;
  background: $uni-bg-color;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
  .num {
    color: rgba(195, 149, 149, 1);
    font-size: 24rpx;
    line-height: 34rpx;
    margin-bottom: 24rpx;
  }
  .battery-info {
    width: 64rpx;
    height: 92rpx;
    border: 1px solid rgba(43, 42, 47, 1);
    padding: 3px;
    text-align: center;
    border-radius: 8rpx;
    position: relative;
    margin: 0 auto;
    &::after {
      display: block;
      content: "";
      width: 24rpx;
      height: 8rpx;
      background: rgba(43, 42, 47, 1);
      border-radius: 2rpx;
      position: absolute;
      bottom: 100%;
      left: 50%;
      margin-left: -12rpx;
      border-top-left-radius: 2rpx;
      border-top-right-radius: 2rpx;
    }
    .box {
      width: 100%;
      height: 100%;
      position: relative;
    }
    .percent {
      font-size: 20rpx;
      color: $uni-main-color;
      width: 100%;
      position: relative;
      z-index: 2;
    }
    .fault {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .iconfont {
        font-size: 40rpx;
      }
    }
    .power {
      position: absolute;
      width: 100%;
      bottom: 0;
    }
  }
  .text {
    font-size: 24rpx;
  }

  .power {
    position: absolute;
    width: 100%;
    bottom: 0;
    z-index: 0;
  }

  .battery-content {
    text-align: center;
    background-color: transparent;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 1;
    .battery-content-title {
      margin-top: 12rpx;
      height: 44rpx;
      font-size: 32rpx;
      font-family: AlibabaPuHuiTi_2_55_Regular;
      color: #222222;
      line-height: 44rpx;
    }

    .battery-bt-content {
      margin-top: 10rpx;
      font-family: AlibabaPuHuiTi_2_55_Regular;
      font-size: 20rpx;
      color: #222222;
      .voltage {
        line-height: 28rpx;
      }

      .temperature {
        line-height: 28rpx;
      }
    }
  }

  .click-show {
    position: absolute;
    right: 0;
    bottom: 0;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 16rpx;
    z-index: 5;
    backdrop-filter: blur(4rpx);

    .box{
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 28rpx;
    }
  }
}
</style>
