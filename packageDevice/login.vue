<template>
  <view>
    <!-- <view @click="close">取消授权</view> -->
  </view>
</template>

<script>
import { dingTalkClientId } from "@/common/env";
import serveApi from "@/apiService";
import { onBeforeMount } from "vue";
//#ifdef MP-DINGTALK
import {
  openAuthMiniApp,
  disposeAuthData,
} from "dingtalk-design-libs/biz/openAuthMiniApp";
//#endif

export default {
  onShow() {
    const that = this;
    //#ifdef MP-DINGTALK
    disposeAuthData(async (options) => {
      if (options.status == "ok") {
        let { authCode } = options.result;
        if (authCode) {
          uni.showLoading({
            title: "正在登录",
          });
          that.phoneLogin(authCode);
        }
      }
    });
    //#endif
  },
  onReady() {
    this.login();
  },
  setup() {
    onBeforeMount(() => {
      uni.setNavigationBarTitle({
        title: "登录",
      });
    });
  },
  methods: {
    login() {
      uni.showLoading({
        title: "正在登录",
      });
      //#ifdef MP-DINGTALK
      dd.getAuthCode({
        success: async (res) => {
          try {
            const {
              data: { data, code },
            } = await serveApi.thirdUserLogin({
              dingTalkCropId: dd.corpId || getApp().globalData.corpId,
              code: res.authCode,
              deviceType: "DING_TALK_MINI_APP",
            });
            // uni.setStorageSync("thirdUserId", data.thirdUserId);
            uni.hideLoading();
            if (code === 0 && data.bizCode === 10012) {
              // this.showAuth();    // 以前是需要调到钉钉默认home页面
              uni.reLaunch({
                url: "/pages/bind/unBinding",
              });
              // 调取第三方登录
            }
            if (code === 0 && data.bizCode !== 10012) {
              uni.setStorageSync("accessToken", data.token);
              uni.setStorageSync("corpId", dd.corpId);
              uni.reLaunch({
                url: "/pages/device/index",
              });
            }
          } catch (error) {
            uni.hideLoading();
          }
        },
      });
      //#endif

      //#ifdef MP-WEIXIN
      wx.login({
        success: async (res) => {
          try {
            const {
              data: { data, code },
            } = await serveApi.thirdUserLogin({
              code: res.code,
              deviceType: "WECHAT_MINI_APP",
            });
            uni.setStorageSync("userId", data.userId);
            uni.hideLoading();
            console.log("[ code,data ] >", code, data);
            if (code === 0 && data.bizCode === 10012) {
              uni.reLaunch({
                url: "/pages/bind/unBinding",
              });
              // 调取第三方登录
            }
            if (code === 0 && data.bizCode !== 10012) {
              uni.setStorageSync("accessToken", data.token);
              uni.reLaunch({
                url: "/pages/device/index",
              });
            }
          } catch (error) {
            uni.hideLoading();
          }
        },
        fail: (err) => {},
      });
      //#endif
      uni.hideLoading();
    },
    showAuth() {
      // this.flag = true;
      // return;
      return openAuthMiniApp({
        path: "pages/home/<USER>", //不要改,这里是小程序dingwlanwvdmrtjjwdmd下的一个页面地址
        panelHeight: "percent50",
        extraData: {
          clientId: dingTalkClientId, // 应用ID(唯一标识)
          rpcScope: "Contact.User.Read",
          fieldScope: "Contact.User.mobile",
          type: 0,
          ext: JSON.stringify({}),
          from: "",
        },
      });
    },

    // close() {
    //   openAuthMiniApp({
    //     path: "pages/cancel/index",
    //     extraData: {
    //       clientId: dingTalkClientId,
    //       ext: "{}",
    //       from: "", // 'aliCloud'
    //     },
    //   });
    // },
    async phoneLogin(authCode) {
      let id;
      //#ifdef MP-DINGTALK
      id = dd.corpId;
      //#endif
      const params = {
        dingTalkCropId: id,
        authCode: authCode,
        deviceType: "DING_TALK_MINI_APP",
      };
      uni.hideLoading();
      const {
        data: { data, code },
      } = await serveApi.getAuthorizationPhone(params);
      if (code === 0) {
        uni.setStorageSync("accessToken", data.token);
        uni.setStorageSync("corpId", id);
        uni.reLaunch({
          url: "/pages/device/index",
        });
      }
    },
  },
};
</script>
