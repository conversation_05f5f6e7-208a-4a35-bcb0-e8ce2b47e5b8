<template>
	<view class="cabinet" v-if="rackList.length">
		<tabs-filter :selectedValue="activeId" :textColor="true" :options="tabs" @change="changeTabs" :scrollX="true" />
		<view class="flex cabinet-name">
			<!-- <view class="name">{{ activeIndex + 1 }}#电池簇</view> -->
			<view class="name">电池簇详细数据</view>
			<view class="btn" @click="showBatteryDetail">单体信息</view>
		</view>
		<view class="cabinet-info">
			<view class="battery">
				<battery :soc="Number(info.soc)" :failed="info.runStatus" />
				<view class="battery-info">
					<view>
						<view class="name">实时电压/V</view>
						<!-- voltage -->
						<view class="num">{{ Number(info.totalVoltage).toFixed(0) || 0 }}</view>
					</view>
					<view>
						<view class="name">实时电流/A</view>
						<!-- electricity -->
						<view class="num">{{ Number(info.totalElectricity).toFixed(0) || 0 }}</view>
					</view>
					<view>
						<view class="name">SOH/%</view>
						<view class="num">{{ Number(info.soh).toFixed(0) || 0 }}</view>
					</view>
				</view>
			</view>
			<view class="detail">
				<view class="detail-info">
					<view>
						<view class="name">单体电压最小值/最小ID</view>
						<view class="num"
							><text>{{ info.minVoltage || 0 }}</text>
							<text class="text">v/ </text>
							<text>{{ info.minVoltageId || 0 }}</text></view
						>
					</view>
					<view>
						<view class="name">单体电压最大值/最大ID</view>
						<view class="num"
							><text>{{ info.maxVoltage || 0 }}</text>
							<text class="text">v/</text>
							<text>{{ info.maxVoltageId || 0 }}</text></view
						>
					</view>
				</view>
				<view class="detail-title"
					><text>单体电压平均值：</text><text class="text">{{ info.avgVoltage || 0 }}</text> <text>v</text></view
				>
				<view class="detail-info">
					<view>
						<view class="name">单体温度最小值/最小ID</view>
						<view class="num"
							><text>{{ info.minTemperature || 0 }}</text>
							<text class="text">°C/</text>
							<text>{{ info.minTemperatureId || 0 }}</text></view
						>
					</view>
					<view>
						<view class="name">单体温度最大值/最大ID</view>
						<view class="num"
							><text>{{ info.maxTemperature || 0 }}</text>
							<text class="text">°C/</text>
							<text>{{ info.maxTemperatureId || 0 }}</text></view
						>
					</view>
				</view>
				<view class="detail-title"
					><text>单体温度平均值：</text><text class="text">{{ info.avgTemperature || 0 }}</text
					><text>°C</text></view
				>
			</view>
		</view>
		<view @touchmove.stop.prevent>
			<uni-popup class="uniPopup" ref="popup" type="bottom" :safe-area="true" mask-background-color="rgba(20, 20, 20, 0.8)" @change="popupChange">
				<view class="popup">
					<view class="header">
						<!-- <text>单体信息</text> -->
						<view class="closeBtn" @click="closePopup">
							<i class="iconfont icon-a-ica-22"></i>
						</view>
					</view>
					<view class="batteries">
						<swiper class="swiper" :duration="duration">
							<swiper-item v-for="item in batteries" :key="item.sortNo">
								<view class="title">{{ item.sortNo }}号电池组</view>
								<view class="batteries-box">
									<view class="ul">
										<view class="list" v-for="(items, indexs) in item.batteries" :key="indexs" @click="listClick(items.sortNo)">
											<battery-col :info="items" :index="items.sortNo" :clickIndex="clickIndex" />
										</view>
									</view>
								</view>
							</swiper-item>
							<view v-if="!batteries?.length" class="empty-box">
								<empty tips="没有单体信息～" style="width: 100%" />
							</view>
						</swiper>
					</view>
				</view>
			</uni-popup>
		</view>
	</view>
</template>
<script>
import { onBeforeMount, onMounted, reactive, toRefs, watch, computed, ref, defineEmits } from "vue";
import battery from "./battery.vue";
import BatteryCol from "./batterCol.vue";
import { word } from "@/common/constant";
import service from "@/apiService/device";
import empty from "../components/empty.vue";
import tabsFilter from "@/components/tabsFilter";
export default {
	components: { battery, BatteryCol, empty, tabsFilter },
	props: {
		deviceSn: String,
	},
	setup(props, { emit }) {
		const { deviceSn } = toRefs(props);
		watch(deviceSn, (val) => {
			if (val) {
				getData(deviceSn.value);
			}
		});
		const state = reactive({
			tabs: [],
			activeId: undefined,
			info: {},
			rackList: [],
			batteries: [],
			activeIndex: 0,
		});
		const changeTabs = (item, index) => {
			state.activeId = item;
			state.activeIndex = index;
			// state.info = state.rackList[index];
			// getData(deviceSn.value);
			getBatteryRack({ stationNo: deviceSn.value, containerNo: item });
		};
		onMounted(async () => {});
		const getData = async (deviceSn) => {
			//  获取电池簇信息 ... 机柜信息
			let result = await service.getBatteryRackList(deviceSn);
			state.rackList = result.data.data;
			state.tabs = state.rackList.map((item, index) => {
				return {
					// label: item,
					label: "机柜" + item.containerNo.substring(3),
					value: item.containerNo,
				};
			});
			state.activeId = state.rackList[0].containerNo;
			// state.info = state.rackList[0];
			getBatteryRack({ stationNo: deviceSn, containerNo: state.activeId });
		};
		const popup = ref(null);
		const showBatteryDetail = async () => {
			let result = await service.getBatteryPackList(deviceSn.value, state.activeId);
			state.batteries = result.data.data;
			popup.value.open("bottom");
			//
		};
		const closePopup = () => {
			popup.value.close();
		};

		const popupChange = (e) => {
			console.log("[ popup ] >");
			emit("hidePopup", e.show);
		};

		const getBatteryRack = async (params) => {
			const {
				data: { code, data },
			} = await service.getBatteryRack(params);
			if (code === 0) {
				state.info = {
					...data,
				};
			}
		};

		const clickIndex = ref(null);
		const listClick = (index) => {
			clickIndex.value = index;
		};
		const duration = ref(1500);
		return {
			...toRefs(state),
			changeTabs,
			popup,
			showBatteryDetail,
			listClick,
			clickIndex,
			duration,
			popupChange,
			closePopup,
		};
	},
};
</script>

<style lang="scss" scoped>
.cabinet {
	padding: 8rpx 28rpx 32rpx;
	background: #fff;
}
.tabs {
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;
	gap: 48rpx;
	.tab {
		text-align: center;
		position: relative;
		padding-bottom: 16rpx;
		color: $uni-secondary-color;
		&::after {
			display: block;
			content: "";
			width: 48rpx;
			height: 8rpx;
			border-radius: 4rpx;
			position: absolute;
			left: 50%;
			margin-left: -24rpx;
			bottom: 0;
			background: transparent;
		}
		&.active {
			color: $uni-primary;
			&::after {
				background: $uni-primary;
			}
		}
		.num {
			font-size: 32rpx;
			line-height: 48rpx;
			font-weight: bold;
			color: #1e312b;
		}
		.name {
			font-size: 24rpx;
			line-height: 40rpx;
			color: rgba(30, 49, 43, 0.6);
		}
	}
}
.cabinet-name {
	justify-content: space-between;
	align-items: center;
	padding-bottom: 16rpx;
	.name {
		font-size: 28rpx;
		line-height: 40rpx;
		color: $uni-main-color;
	}
	.btn {
		width: 158rpx;
		height: 56rpx;
		line-height: 56rpx;
		text-align: center;
		font-size: 28rpx;
		border-radius: 8rpx;
		background: $uni-primary;
		color: #fff;
	}
}
.cabinet-info {
	padding: 24rpx;
	background: #f6f7f7;
	border-radius: 16rpx;
	.battery {
		background: #ffffff;
		border-radius: 16rpx;
		padding: 24rpx 32rpx;
	}
	.battery-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 40rpx;
		text-align: center;
		.name {
			font-size: 28rpx;
			line-height: 40rpx;
			height: 40rpx;
			color: $uni-secondary-color;
			margin-bottom: 26rpx;
		}
		.num {
			font-size: 32rpx;
			line-height: 32rpx;
			height: 32rpx;
			color: $uni-main-color;
			font-weight: bold;
		}
	}
	.detail {
		.detail-info {
			display: flex;
			justify-content: space-between;
			align-items: center;
			text-align: center;
			margin-top: 24rpx;
			.name {
				font-size: 28rpx;
				line-height: 40rpx;
				color: $uni-secondary-color;
				margin-bottom: 16rpx;
			}
			.num {
				font-size: 28rpx;
				line-height: 40rpx;
				color: $uni-main-color;
				.text {
					color: $uni-secondary-color;
				}
			}
		}
		.detail-title {
			font-size: 28rpx;
			line-height: 64rpx;
			color: $uni-secondary-color;
			margin-top: 24rpx;
			padding: 0 24rpx;
			background: #ffffff;
			border-radius: 16rpx;
			.text {
				color: $uni-main-color;
			}
		}
	}
}
.popup {
	height: 95vh;
	padding: 24rpx;
	padding-bottom: 40rpx;
	box-shadow: 0px -20px 20px 0px rgba(0, 0, 0, 0.1);
	background: #fff;
	border-top-left-radius: 16rpx;
	border-top-right-radius: 16rpx;
	.header {
		color: $uni-secondary-color;
		// padding-bottom: 16rpx;
		position: relative;
		padding-right: 48rpx;
		background: #fff;
		.closeBtn {
			position: absolute;
			right: 0;
			top: 0;
			width: 48rpx;
			height: 48rpx;
			line-height: 48rpx;
			z-index: 99;
			.iconfont {
				font-size: 48rpx;
				color: $uni-help-color;
			}
		}
	}
	.batteries {
		height: calc(100% - 48rpx);
		overflow-y: scroll;
		display: flex;
		flex-direction: column;
		.swiper {
			flex: 1;
			.title {
				font-size: 24rpx;
				line-height: 48rpx;
				color: $uni-secondary-color;
			}
			.empty-box {
				width: 100%;
				text-align: center;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
			}
		}
		.batteries-box {
			padding-left: 1rpx;
			width: 100%;
			height: calc(100% - 48rpx);
			overflow-y: scroll;
		}
		.ul {
			width: 100%;
			display: flex;
			flex-wrap: wrap;
			gap: 16rpx;
			justify-content: first baseline;
			align-items: flex-start;
			overflow: hidden;
		}
		.list {
			width: calc(16% - 10rpx);
			color: $uni-main-color;
		}
	}
}
:deep(.uni-popup .uni-popup__wrapper) {
	z-index: 9999;
}
.cabinet {
	position: relative;
	z-index: 999;
}
.uniPopup {
	position: relative;
	z-index: 999;
}
</style>
<style lang="scss">
.tabs {
	padding-left: 0;
	padding-right: 0;
}
</style>
