<template>
  <view :style="detailStyle">
    <view class="box-content">
      <view class="station-box">
        <view class="station-content">
          <view class="station-img" @click="openAlert">
            <img
              :src="detail?.stationPic || '../static/newIcon.png'"
              style="width: 100%"
              class="image"
              mode="aspectFill"
            />
          </view>
          <view class="station-data">
            <view class="station-data-item">
              <view class="item-data">{{
                detail?.installedCapacity || 0
              }}</view>
              <view class="item-title">装机容量(kWh)</view>
            </view>
            <view class="station-data-item">
              <view class="item-data">{{ detail?.installedPower || 0 }}</view>
              <view class="item-title">装机功率(KW)</view>
            </view>
            <view class="station-data-item">
              <view class="item-data"
                >{{
                  detail?.createTime
                    ? dayjs(detail?.createTime).format("YYYY/MM/DD")
                    : ""
                }}
              </view>
              <view class="item-title">投运日期</view>
            </view>
          </view>
          <view class="station-addss">
            <view class="textImg"
              ><img src="@/static/dingwei.svg" class="iconSvg"
            /></view>
            <text> {{ detail?.address }}</text>
          </view>
          <view class="station-vpp" v-if="vppInfo?.openVpp && 1 > 2">
            <text class="textImg"
              ><img src="@/static/ai.png" class="iconSvg"
            /></text>
            <view class="tag tag-1" v-if="dayDiff <= 0">智能均衡模式</view>
            <view class="tag tag-2" v-if="vppInfo?.openVpp"
              >需求响应已启用</view
            >
          </view>
        </view>
      </view>
      <view class="device-box">
        <view class="device">
          <view class="device-state">
            <i
              class="iconfont state-icon"
              :class="getState(detail?.status).icon"
            ></i
            ><text class="device-text">{{
              getState(detail?.status).label
            }}</text>
          </view>
          <view class="guage-chart">
            <gauge
              v-if="detail?.soc === 0 || detail?.soc > 0"
              :soc="Number(detail?.soc) || 0"
            />
          </view>
          <view
            class="faultTag flex"
            @click="gotoFailure(detail)"
            v-if="detail?.alarmQuantity"
          >
            <i class="iconfont error icon-ica-dianchi-guzhang"></i>
            <text class="text">风险 {{ detail?.alarmQuantity }} 条</text>
          </view>
        </view>
      </view>
      <view class="bar-chart">
        <view class="device-data">
          <text>soh「</text>
          <text>{{ soh }}</text>
          <text>」· soc「</text>
          <text>{{ soc }}</text>
          <text>」· 功率「</text>
          <text>{{ bmsPower }}</text>
          <text>」· 温度「</text>
          <text>{{ avgTemperature }}</text>
          <text>」</text>
        </view>

        <!-- <button
          class="primary"
          style="width: 240rpx; margin: 32rpx auto"
          @click="gotoStrategy()"
        >
          电池执行策略
        </button> -->
        <!-- 总充电量统计 -->
        <view class="device-chart">
          <view class="tabs">
            <view class="tab">
              <view class="num">{{
                unitConversion(info.totalCharge, 1000) || 0
              }}</view>
              <view class="name">
                {{
                  alternateUnits(info.totalCharge, 1000)
                    ? "总充电量(MWh)"
                    : "总充电量(kWh)"
                }}</view
              >
            </view>
            <view class="tab">
              <view class="num">{{
                unitConversion(info.totalDischarge, 1000) || 0
              }}</view>
              <view class="name">
                {{
                  alternateUnits(info.totalDischarge, 1000)
                    ? "总放电量(MWh)"
                    : "总放电量(kWh)"
                }}
              </view>
            </view>
            <view class="tab">
              <view class="num"
                >{{ unitConversion(info.totalProfit, 10000) || 0 }}
              </view>
              <view class="name">
                {{
                  alternateUnits(info.totalProfit, 10000)
                    ? "累计收益(万元)"
                    : "累计收益(元)"
                }}
              </view>
            </view>
          </view>
        </view>
        <view class="bg-line"></view>
        <view class="working">
          <view class="bg-ff rounded-lg p-4">
            <view class="mb-3 leading-8 working-status-title"
              >整体运行状态</view
            >
            <view style="height: 440rpx" class="working-status">
              <working-status
                :bmsPower="detail?.bmsPower"
                :gridPower="detail?.gridPower"
                :status="detail?.status"
              />
            </view>
          </view>
          <view class="trend-charts">
            <view class="charts-title"> 充放电&收益趋势 </view>
            <view class="content">
              <bar
                :fromType="tabActive"
                :chartData="weekdata.series"
                :dateTime="weekdata.dateArray"
                width="646"
                height="400"
              />
            </view>
          </view>
        </view>
        <view class="bg-line"></view>
        <view class="soc-chart">
          <view class="soc-header"
            ><text>24小时电量变化</text>
            <view class="legend">
              <image
                src="@/static/lightning-1.svg"
                style="width: 32rpx; height: 32rpx"
              /><text>充电</text>
              <image
                src="@/static/lightning-2.svg"
                style="width: 32rpx; height: 32rpx; margin-left: 24rpx"
              />
              <text>放电</text>
            </view>
            <view class="choose-day">
              <picker
                @change="bindPickerChange"
                :value="socDayIndex"
                :range="socDayShowOptions"
              >
                <view class="picker"
                  ><text>{{ socDayShowOptions[socDayIndex] }}</text>
                  <i class="iconfont icon-a-ica-dianchi-guzhangbeifen7"></i
                ></view>
              </picker>
            </view>
          </view>
          <!-- <canvas canvas-id="electricityChart" id="electricityChart" type="2d" style="width: 100%; height: 170px; z-index: 1" :width="canvasWidth" :height="170 * dpr"></canvas> -->
          <!-- #ifdef MP-DINGTALK -->
          <canvas
            canvas-id="electricityChart"
            id="electricityChart"
            type="2d"
            :style="{ width: '100%', height: 170 + 'px' }"
            :width="canvasWidth"
            :height="170 * dpr"
            class="charts"
          />
          <!-- #endif -->
          <!-- #ifdef MP-WEIXIN -->
          <canvas
            canvas-id="electricityChart1"
            id="electricityChart1"
            style="width: 100%; height: 170px"
            :width="canvasWidth"
            :height="170 * dpr"
            :class="hideChart ? 'hideChart' : 'showChart'"
          ></canvas>
          <!-- #endif -->
        </view>
      </view>
      <view class="box-bg"></view>
    </view>
    <!-- <view class="bg-line"></view> -->
    <!-- <view class="prediction">
			<div class="prediction-title">
				<div class="prediction-title-text">今日运行轨迹预测</div>
				<img src="@/static/ai.png" class="iconSvg" />
			</div>
			<div class="prediction-data">
				<div class="prediction-data-li">
					<div class="inline-block text-left">
						<div class="text-secondar-text">
							实时累计充电量(
							{{ alternateUnits(RealStrategyAndAiRecommendStrategy.realCharge || 0, 1000) ? "MWh" : "kWh" }})
						</div>
						<div class="text-base font-medium">
							{{ unitConversion(RealStrategyAndAiRecommendStrategy.realCharge || 0, 1000) }}
						</div>
					</div>
				</div>
				<div class="line"></div>
				<div class="prediction-data-li">
					<div class="inline-block text-left">
						<div class="text-secondar-text">
							预测充电量(
							{{ alternateUnits(RealStrategyAndAiRecommendStrategy.aiRecommendCharge || 0, 1000) ? "MWh" : "kWh" }})
						</div>
						<div class="text-base font-medium">
							{{ unitConversion(RealStrategyAndAiRecommendStrategy.aiRecommendCharge || 0, 1000) }}
						</div>
					</div>
				</div>
			</div>
			<div class="prediction-data">
				<div class="prediction-data-li">
					<div class="inline-block text-left">
						<div class="text-secondar-text">实时累计放电量({{ alternateUnits(RealStrategyAndAiRecommendStrategy.realDischarge || 0, 1000) ? "MWh" : "kWh" }})</div>
						<div class="text-base font-medium">
							{{ unitConversion(RealStrategyAndAiRecommendStrategy.realDischarge || 0, 1000) }}
						</div>
					</div>
				</div>
				<div class="line"></div>
				<div class="prediction-data-li">
					<div class="inline-block text-left">
						<div class="text-secondar-text">预测放电量({{ alternateUnits(RealStrategyAndAiRecommendStrategy.aiRecommendDischarge || 0, 1000) ? "MWh" : "kWh" }})</div>
						<div class="text-base font-medium">
							{{ unitConversion(RealStrategyAndAiRecommendStrategy.aiRecommendDischarge || 0, 1000) }}
						</div>
					</div>
				</div>
			</div>
		
		</view> -->
    <div class="prediction-chart" v-if="false">
      <running-trajectory :chartData="RealStrategyAndAiRecommendStrategy" />
    </div>
    <view class="bg-line" v-if="1 > 2"></view>
    <div class="records" v-if="1 > 2">
      <div class="records-header">
        <div class="records-title">
          <div class="prediction-title-text">需求响应记录</div>
          <img src="@/static/ai.png" class="iconSvg" />
        </div>
        <div class="records-select">
          <picker
            @change="changeRecordType"
            :value="selectedRecordType"
            :range="selectedRecordOptions"
          >
            <view class="picker"
              ><text>{{ selectedRecordOptions[selectedRecordType] }}</text>
              <i class="iconfont icon-a-ica-dianchi-guzhangbeifen7"></i
            ></view>
          </picker>
        </div>
      </div>
      <div class="records-content">
        <records-table
          :tableData="recordsData"
          :tableColumns="tableColumns"
          :type="selectedRecordType"
        />
      </div>
    </div>
    <view class="bg-line"></view>
    <view>
      <cabinet :deviceSn="deviceSn" @hidePopup="onHideCabinetPopup" />
    </view>

    <upFile
      ref="popup"
      @submit="submit"
      :stationPic="stationPic"
      @hidePopup="onHidePopup"
    />
  </view>
</template>

<script>
import gauge from "./gauge.vue";
// import bar from "./bar.vue";
import bar from "@/components/newBar.vue";
import { cabinet } from "./cabinet.vue";
import {
  reactive,
  toRefs,
  onMounted,
  computed,
  ref,
  getCurrentInstance,
  nextTick,
} from "vue";
import { onShow } from "@dcloudio/uni-app";
import service from "../apiService/device";
import apiVpp from "../apiService/vpp";
import { getState } from "../common/setup";
import { ossStaticResourceUrl } from "@/common/constant";
import { dateFormat } from "@/common/util";
import { DrawCanvas, formatData } from "@/common/drawElectricityChart";
import dayjs from "dayjs";
import upFile from "./upFile.vue";
import { unitConversion, alternateUnits } from "@/common/util";
import WorkingStatus from "../components/workingStatus.vue";
import RunningTrajectory from "../components/runningTrajectory.vue";
import RecordsTable from "../components/recordsTable.vue";
import store from "@/store/index.js";
export default {
  components: {
    gauge,
    bar,
    cabinet,
    upFile,
    WorkingStatus,
    RunningTrajectory,
    RecordsTable,
  },
  setup() {
    onShow(async () => {
      const pages = getCurrentPages();
      const options = pages[pages.length - 1].options;
      state.deviceSn = options.deviceSn;
      uni.setNavigationBarTitle({
        title: options.deviceName,
      });
    });
    const detail = ref({
      soc: undefined,
    });
    const state = reactive({
      tabActive: undefined,
      barChartsData: [],
      deviceSn: undefined,
      weekdata: {},
      info: {},
      SocAndWorkStatusData: {},
      dpr: uni.getSystemInfoSync().devicePixelRatio,
      chargePower: 1,
      socDayShowOptions: [],
      socDayValueOptions: [],
      socDayIndex: 0,
    });
    const canvasWidth = computed(() => {
      let { devicePixelRatio, windowWidth } = uni.getSystemInfoSync();
      return windowWidth * devicePixelRatio;
    });
    const light = computed(() => {
      return `${ossStaticResourceUrl}lightning-2.png`;
    });
    const changeTab = (data) => {
      state.tabActive = data;
    };
    const isOperator = computed(() => {
      return store.state.user.customerInfo.roles.includes("operation_staff");
    });
    const gotoFailure = (data) => {
      const { stationNo } = data;
      uni.navigateTo({
        url: `/pages/user/riakPageD?stationNo=${stationNo}&isOperator=${isOperator.value}`,
      });
    };

    const stationPic = ref(null);

    // const loadingImg = () => {
    // 	return new Promise((ret, rej) => {
    // 		uni.getImageInfo({
    // 			src: require("../static/newIcon.png"),
    // 			success: (res) => {
    // 				ret(res.path);
    // 			},
    // 		});
    // 	});
    // };
    //更新告警

    // 获取设备详情
    const getByDeviceSN = async () => {
      let result = await service.getByDeviceSN(state.deviceSn);
      detail.value = result.data.data;
      for (const key in result.data.data) {
        let value = result.data.data[key];
        detail.value[key] = value;
      }
      detail.value.soc = result.data.data.soc || 0; // 用于测试
      if (detail.value?.stationPic) {
        stationPic.value = { url: detail.value.stationPic, upFile: false };
      } else {
        // const url = await loadingImg();
        stationPic.value = { url: "../static/newIcon.png", upFile: false };
      }
    };

    const today = new Date(); // 获取今天的日期
    const oneWeekAgo = new Date(today.getTime() - 6 * 24 * 60 * 60 * 1000); // 计算出7天前的日期
    const startDate = dateFormat(oneWeekAgo, "YYYY-MM-DD"); // 格式化开始日期
    const endDate = dateFormat(today, "YYYY-MM-DD"); // 格式化结束日期

    const statisticsDailyUsageAndProfit = async () => {
      // 获取图表数据
      let result = await service.statisticsDailyUsageAndProfit({
        stationNo: state.deviceSn,
        startDate: dayjs().subtract(6, "day").format("YYYY-MM-DD"),
        endDate: dayjs().format("YYYY-MM-DD"),
        periodType: "day",
        // type:1//1代表七天
        // startDate: startDate,
        // endDate: endDate,
      });
      // let {data}=result.data.data;
      let data = result.data.data;
      const dateArray = data.map((item) => {
        return dateFormat(item.date, "MM/DD");
      });
      const inChargeArray = data.map((item) => {
        return item.charge;
      });
      const disChargeArray = data.map((item) => {
        return item.discharge;
      });
      const moneyArray = data.map((item) => {
        return item.profit;
      });

      const series = [
        {
          name: "充电量",
          type: "column",
          data: inChargeArray,
          color: "#3ECDDA",
        },
        {
          name: "放电量",
          type: "column",
          data: disChargeArray,
          color: "#73ADFF",
        },
        {
          name: "收益",
          index: 1,
          type: "line",
          data: moneyArray,
          color: "#FD750B",
        },
      ];
      // const x = dateArray.map((item, index) => {
      // 	return index % 4 === 0 ? item : ''
      // })
      state.weekdata = {
        dateArray,
        series,
      };
    };
    const getStastics = async () => {
      let result = await service.statisticStationChargeAndProfitSummary({
        stationNo: state.deviceSn,
      });
      state.info = result.data.data;
    };
    const getLast7Days = () => {
      let resultShow = [],
        resultValue = [];
      const currentDate = dayjs();
      for (let index = 0; index < 7; index++) {
        let day = currentDate.subtract(index, "day");
        resultShow.push(day.format("MM月DD日"));
        resultValue.push(day.format("YYYY-MM-DD"));
      }
      state.socDayShowOptions = resultShow;
      state.socDayValueOptions = resultValue;
      state.socDayIndex = 0;
    };
    const getStatisticsSocAndWorkStatusByDay = async () => {
      let result = await service.statisticsSocAndWorkStatusByDay(
        state.deviceSn,
        state.socDayValueOptions[state.socDayIndex]
      );
      state.SocAndWorkStatusData = result.data.data;
      drawElectricity();
    };
    const bindPickerChange = ({ detail }) => {
      console.log("[ detail ] >", detail);
      if (state.socDayIndex == detail.value) return;
      state.socDayIndex = detail.value;
      getStatisticsSocAndWorkStatusByDay();
    };
    const tableColumns = ref([
      {
        title: "日期",
        key: "date",
        align: "center",
      },
      {
        title: "中标容量 (kWh)",
        key: "bidPower",
        align: "center",
      },
      {
        title: "中标价格 (元/kWh)",
        key: "predictAmount",
        align: "center",
      },
      {
        title: "有效响应容量 (kWh)",
        key: "discharge",
        align: "center",
      },
      {
        title: "预计收益 (元)",
        key: "profit",
        align: "center",
      },
    ]);
    const recordsData = ref([]);
    const getVppDemandRecords = async () => {
      // 1 > 2
      // const res0 = await apiVpp.getVppDemandRecords({
      // 	stationId: detail.value.id,
      // 	status: selectedRecordType.value,
      // });
      // recordsData.value = res0.data.data || [];
    };
    const instance = getCurrentInstance();
    onMounted(async () => {
      await getByDeviceSN();
      changeTab("inChargeArray");
      getLast7Days();
      getStatisticsSocAndWorkStatusByDay();

      await statisticsDailyUsageAndProfit();
      await getStastics();
      // await getVppInfo();
      await getRealStrategyAndAiRecommendStrategy();
      // await getVppDemandRecords();
    });
    const drawElectricity = async () => {
      nextTick(() => {
        let data = formatData(state.SocAndWorkStatusData);
        //#ifdef MP-DINGTALK
        const context = uni.createCanvasContext("electricityChart");
        var painter = new DrawCanvas(context, canvasWidth.value, 170);
        painter.update(data);
        painter.clear();
        painter.paint();
        //#endif

        //#ifdef MP-WEIXIN

        const context1 = uni.createCanvasContext("electricityChart1");
        var painter1 = new DrawCanvas(context1);
        context1.scale(1 / state.dpr, 1 / state.dpr);
        painter1.update(data);
        painter1.clear();
        painter1.paint();

        //#endif
      });
    };

    const gotoStrategy = () => {
      uni.navigateTo({
        url: "/packageDevice/strategy?deviceSn=" + state.deviceSn,
      });
    };

    const popup = ref(null);
    const openAlert = () => {
      popup.value.openPopup();
      detailStyle.value = "overflow:hidden;height:100vh";
    };

    const submit = async (url) => {
      const {
        data: { code },
      } = await service.updateStationInfo({
        stationPic: url,
        stationNo: state.deviceSn,
      });
      if (code === 0) {
        popup.value.colsePopup();
        detailStyle.value = "";
        getByDeviceSN();
      }
    };
    const dayDiff = ref();
    const vppInfo = ref({});
    const switchVal = ref();
    const getVppInfo = async () => {
      const res = await apiVpp.getVppStationInfo(detail.value.id);
      vppInfo.value = res.data.data;
      switchVal.value = res.data.data.openVppDemand || 0;
      // vppInfo.value.openVpp = 1 // 用于测试是否开启ai
      // vppInfo.value.openVppTime = new Date('2024-07-17') // 用于测试时间，不用于正式
      // 计算剩余天数
      if (!vppInfo.value.openVppTime) {
        dayDiff.value = 15;
      } else {
        const timeDiff =
          new Date(dayjs(res.data.data.openVppTime)) - new Date(dayjs());
        dayDiff.value = 15 + Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
      }
    };
    const RealStrategyAndAiRecommendStrategy = ref({});
    const getRealStrategyAndAiRecommendStrategy = async () => {
      // 1 > 2
      const params = {
        stationId: detail.value.id,
        runDt: dayjs().format("YYYY-MM-DD"),
      };
      const res = await apiVpp.getRealStrategyAndAiRecommendStrategy(
        detail.value.id,
        params.runDt
      );
      RealStrategyAndAiRecommendStrategy.value = res.data.data;
    };
    const selectedRecordOptions = ref(["待响应", "已响应"]);
    const selectedRecordType = ref(0);
    const changeRecordType = (e) => {
      const oldType = selectedRecordType.value;
      if (oldType === e.detail.value) return;
      selectedRecordType.value = e.detail.value;
      getVppDemandRecords();
      // TODO: 选择响应记录
    };
    const hideChart = ref(false);
    const detailStyle = ref("");
    const onHideCabinetPopup = (e) => {
      if (e) {
        setTimeout(() => {
          hideChart.value = e;
        }, 200);
      } else {
        hideChart.value = e;
      }
    };
    const onHidePopup = (e) => {
      console.log("[ e popup状态 ] >", e);
      if (e) {
        detailStyle.value = "overflow:hidden;height:100vh";
        setTimeout(() => {
          hideChart.value = e;
        }, 200);
      } else {
        hideChart.value = e;
        detailStyle.value = "";
      }
    };
    const soh = computed(() => {
      return detail.value?.soh ? detail.value?.soh + "%" : "-";
    });
    const soc = computed(() => {
      return detail.value?.soc ? detail.value?.soc + "%" : "-";
    });
    const bmsPower = computed(() => {
      return detail.value?.status === 3
        ? "-"
        : detail.value?.bmsPower
        ? detail.value?.bmsPower + "KW"
        : 0;
    });
    const avgTemperature = computed(() => {
      return detail.value?.avgTemperature
        ? detail.value?.avgTemperature + "℃"
        : "-";
    });

    return {
      ...toRefs(state),
      changeTab,
      gotoFailure,
      getState,
      getByDeviceSN,
      ossStaticResourceUrl,
      getStastics,
      dateFormat,
      drawElectricity,
      gotoStrategy,
      getStatisticsSocAndWorkStatusByDay,
      canvasWidth,
      bindPickerChange,
      dayjs,
      openAlert,
      popup,
      submit,
      stationPic,
      unitConversion,
      alternateUnits,
      dayDiff,
      vppInfo,
      RealStrategyAndAiRecommendStrategy,
      getRealStrategyAndAiRecommendStrategy,
      selectedRecordOptions,
      changeRecordType,
      selectedRecordType,
      recordsData,
      tableColumns,
      isOperator,
      light,
      onHideCabinetPopup,
      onHidePopup,
      hideChart,
      detail,
      soh,
      soc,
      bmsPower,
      avgTemperature,
      detailStyle,
    };
  },
  onLoad(option) {
    // 页面第一次从tabbar其他页面跳转进来会触发，否则不会。 详情页进来会触发。
    uni.$once("refreshDetail", async () => {
      console.log("[ 刷新详情页 ] >");
      // 在这里执行onLoad逻辑
      await this.getByDeviceSN();
    });
  },
};
</script>

<style lang="scss" scoped>
.guage-chart {
  height: 320rpx;
}
.picker {
  font-size: 24rpx;
  background: #f6f7f7;
  border-radius: 28rpx;
  height: 56rpx;
  line-height: 56rpx;
  color: rgba(34, 34, 34, 0.6);
  padding: 0 14rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .iconfont {
    transform: rotate(90deg);
    color: rgba(34, 34, 34, 0.6);
    width: 32rpx;
    height: 32rpx;
    text-align: center;
    line-height: 32rpx;
  }
}

.device-box {
  position: relative;
  // padding-bottom: 80rpx;
  background: #fff;
  z-index: 2;
}

.device {
  position: relative;
  width: 100%;
  padding-top: 34rpx;
  position: relative;
  z-index: 3;

  .device-state {
    position: absolute;
    left: 34rpx;
    top: 22rpx;
    display: flex;
    vertical-align: middle;
    font-size: 24rpx;
    align-items: center;
    padding: 0 24rpx;
    height: 64rpx;
    line-height: 64rpx;
    box-shadow: 0px 4rpx 16rpx 0px rgba(0, 0, 0, 0.1);
    border-radius: 32rpx;

    .iconfont {
      font-size: 44rpx;
      margin-right: 6rpx;
      vertical-align: middle;

      &.state-icon {
        color: $uni-primary;
      }
    }

    .device-text {
      line-height: 40rpx;
      vertical-align: middle;
    }
  }

  .faultTag {
    position: absolute;
    right: 24rpx;
    top: 22rpx;
    display: inline-flex;
    vertical-align: middle;
    align-items: center;
    height: 64rpx;
    line-height: 64rpx;
    color: #fd0b0b;
    background: #fff;
    box-shadow: 0px 4rpx 16rpx 0px rgba(0, 0, 0, 0.1);
    border-radius: 32rpx;
    padding: 0 24rpx;

    .iconfont {
      font-size: 40rpx;
      margin-right: 8rpx;
      line-height: 44rpx;
    }

    .text {
      font-size: 24rpx;
      line-height: 44rpx;
    }
  }
}

.device-data {
  font-size: 24rpx;
  text-align: center;
  // color: $uni-extra-color;
  color: #222222;
  position: relative;
  z-index: 9;
  margin-bottom: 32rpx;
}

.box-content {
  position: relative;
  z-index: 2;

  .box-bg {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: #fff;
    z-index: 1;
  }

  .station-box {
    position: relative;
    z-index: 4;
    padding: 16rpx 28rpx 0 28rpx;
    color: #000000;

    .station-content {
      background: #f6f7f7;
      border-radius: 16rpx;
      padding-bottom: 32rpx;

      // backdrop-filter: blur(10px);
      .station-img {
        height: 387rpx;
        overflow: hidden;
        border-radius: 16rpx;

        image {
          height: 100%;
        }
      }

      .station-data {
        display: flex;
        margin-top: 24rpx;

        .station-data-item {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;

          &:after {
            position: absolute;
            content: "";
            width: 2rpx;
            height: 84rpx;
            background: rgba(34, 34, 34, 0.08);
            top: 50%;
            transform: translateY(-50%);
            right: 0;
          }

          &:last-child {
            &:after {
              display: none;
            }
          }

          .item-data {
            font-size: 32rpx;
            font-weight: 500;
            color: #222222;
            line-height: 48rpx;
            display: flex;
            align-items: center;

            .unit {
              margin-left: 10rpx;
            }
          }

          .item-title {
            font-size: 24rpx;
            color: #222222;
            line-height: 40rpx;
          }
        }
      }

      .station-addss {
        font-size: 24rpx;
        color: #222222;
        line-height: 44rpx;
        padding-top: 16rpx;
        padding-left: 24rpx;
        display: flex;

        .textImg {
          margin-right: 12rpx;
          margin-top: 4rpx;
        }

        .iconSvg {
          width: 25rpx;
          height: 25rpx;
        }
      }

      .station-vpp {
        font-size: 24rpx;
        color: #222222;
        line-height: 44rpx;
        padding-top: 16rpx;
        padding-left: 24rpx;
        display: flex;
        align-items: center;

        .textImg {
          margin-right: 12rpx;
          margin-top: 4rpx;
        }

        .iconSvg {
          width: 25rpx;
          height: 25rpx;
        }

        .tag {
          padding: 0 16rpx;
          line-height: 44rpx;
          font-size: 24rpx;
          border-radius: 4rpx;

          &.tag-1 {
            color: rgba(22, 119, 255, 1);
            background: rgba(22, 119, 255, 0.1);
          }

          &.tag-2 {
            color: rgba(125, 74, 249, 1);
            background: rgba(125, 74, 249, 0.1);
            margin-left: 20rpx;
          }
        }
      }
    }
  }
}

.bar-chart {
  position: relative;
  z-index: 4;
  margin-top: -30rpx;

  .device-chart {
    padding: 0 28rpx;
    padding-top: 24rpx;
    padding-bottom: 32rpx;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
  }

  .tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;

    .tab {
      text-align: center;
      position: relative;
      // padding-bottom: 16rpx;
      width: 33.333%;

      & + .tab {
        border-left: 1px solid $uni-border-1;
        margin: 0;
      }

      .num {
        font-size: 32rpx;
        line-height: 48rpx;
        font-weight: bold;
        color: #1e312b;
      }

      .name {
        font-size: 24rpx;
        line-height: 40rpx;
        color: rgba(30, 49, 43, 0.6);
      }

      &::after {
        display: block;
        content: "";
        width: 96rpx;
        height: 8rpx;
        border-radius: 4rpx;
        background: transparent;
        position: absolute;
        left: 50%;
        margin-left: -48rpx;
        bottom: 0;
      }

      &.active {
        &::after {
          background: $uni-primary;
        }
      }
    }
  }

  .device-data-bottom {
    padding: 32rpx 24rpx;
    background: #fff;

    .tabs {
      margin-bottom: 0;

      .tab {
        padding-bottom: 0;
      }
    }
  }
}

.device-chart {
  background: rgba(246, 247, 247, 0.1);
  // box-shadow: 0px 8rpx 16rpx 0px rgba(0, 0, 0, 0.1);
  // border-radius: 16rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  // backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;

  // .filter {
  //   width: 100%;
  //   height: 100%;
  //   position: absolute;
  //   z-index: 1;
  //   left: 0;
  //   right: 0;
  //   background: rgba(255, 255, 255, 1);
  //   filter: blur(10px);
  // }
  .tabs {
    // margin-bottom: 24rpx;
    position: relative;
    z-index: 3;
  }

  .content {
    position: relative;
    z-index: 3;
    padding-top: 20rpx;
    background: #f6f7f7;
    border-bottom-left-radius: 16rpx;
    border-bottom-right-radius: 16rpx;
  }
}

.bg-line {
  width: 100%;
  height: 28rpx;
  background: #f6f7f7;
}

.soc-chart {
  padding: 0 28rpx;
  padding-top: 32rpx;
  padding-bottom: 32rpx;

  .soc-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28rpx;
    margin-bottom: 10rpx;

    .choose-day {
      width: 180rpx;
      font-size: 24rpx;
    }
  }

  .legend {
    display: flex;
    align-items: center;
    font-size: 24rpx;
  }
}

.working {
  background: #fff;
  padding: 24rpx 28rpx;
}

.working-status-title {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.working-status {
  padding: 28rpx 28rpx 10rpx 28rpx;
  // border: 1px solid #d9d9d9;
  // border-radius: 4rpx;
}

.trend-charts {
  width: 100%;
  padding: 24rpx;
  background: rgba(246, 247, 247, 1);
  border-radius: 8rpx;
}

.charts-title {
  font-size: 24rpx;
  line-height: 40rpx;
  color: rgba(30, 49, 43, 0.6);
  padding-top: 15px;
  background-color: rgba(246, 247, 247, 1);
  // border-radius: 16rpx;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
}

.prediction {
  padding: 24rpx 28rpx;

  .prediction-title {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .iconSvg {
      width: 28rpx;
      height: 28rpx;
      margin-left: 12rpx;
    }
  }

  .prediction-data {
    display: flex;
    margin-bottom: 24rpx;
    column-gap: 60rpx;
    color: $uni-primary;
    font-weight: bold;

    .line {
      width: 1px;
      height: 68rpx;
      background: $uni-border-1;
    }

    .prediction-data-li {
      flex: 1;
    }

    .text-secondar-text {
      color: rgba(30, 49, 43, 0.6);
      font-weight: normal;
      margin-bottom: 12rpx;
    }
  }
}

.records {
  padding: 48rpx 16rpx;

  .records-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16rpx;
    margin-bottom: 24rpx;

    .records-title {
      display: flex;
      align-items: center;

      .iconSvg {
        width: 28rpx;
        height: 28rpx;
      }
    }
  }
}

.charts {
  /* 判断是否是微信小程序 */
  /* #ifdef MP-WEIXIN */
  width: 694rpx;
  height: 400rpx;
  /* #endif */
}

/* #ifdef MP-WEIXIN */
.hideChart {
  display: none;
}

.showChart {
  display: block;
}

/* #endif */
</style>
<style lang="scss">
.device-data {
  text {
    font-size: 24rpx;
  }
}
</style>
