<template>
	<view class="strategy">
		<template v-if="detail?.workProcessGroupList?.length">
			<view class="device-data-bottom box">
				<view style="color: rgba(34, 34, 34, 0.6); margin-bottom: 24rpx"
					><text>分时电价有效期：{{ detail.effectiveYear }}年</text>
					<text style="margin-left: 24rpx">电价单位：元/kWh</text>
				</view>
				<view class="tabs">
					<template v-for="(value, key) in detail.segmentPrice">
						<view v-if="value || value == 0" class="tab" :key="key">
							<view class="num">{{ showPrice(value) }}</view>
							<view class="name">{{ priceName[key] }}</view>
						</view>
					</template>
				</view>
			</view>
			<view class="box">
				<view class="price-chart" v-for="(item, index) in detail.workProcessGroupList" :key="'workProcessGroupList' + index">
					<view class="head">
						<text class="title">{{ item.season }}</text>
					</view>
					<canvas style="width: 100%; height: 215px; z-index: 1" :canvas-id="'priceChart' + index" :id="'priceChart' + index" :width="canvasWidth" :height="canvasHeight"></canvas>
					<button class="primary large" style="width: 320rpx; margin: 24rpx auto 32rpx" @click="editStrategy(item)">编辑策略</button>
				</view>
			</view>
		</template>
		<view style="width: 100%; text-align: center" v-else>
			<empty value="设备未上报电池工作流程～" style="width: 100%" />
		</view>
	</view>
</template>

<script>
import service from "../apiService/device";
import { onMounted, reactive, toRefs, computed } from "vue";
import { onShow } from "@dcloudio/uni-app";
import { DrawCanvas, formatData, priceName, showPrice } from "@/common/drawPriceChart";
import empty from "@/components/empty.vue";

export default {
	components: { empty },
	setup() {
		const state = reactive({
			deviceSn: undefined,
			detail: {},
		});

		onShow(async () => {
			const pages = getCurrentPages();
			const options = pages[pages.length - 1].options;
			state.deviceSn = options.deviceSn;
		});
		const canvasWidth = computed(() => {
			let { devicePixelRatio, windowWidth } = uni.getSystemInfoSync();
			return windowWidth * devicePixelRatio;
		});
		const canvasHeight = computed(() => {
			let { devicePixelRatio } = uni.getSystemInfoSync();
			return 215 * devicePixelRatio;
		});
		const drawPrice = () => {
			state.detail.workProcessGroupList.forEach((element, index) => {
				let canvasId = "priceChart" + index;
				let data = formatData(element, state.detail.segmentPrice);
				let context = uni.createCanvasContext(canvasId);
				var painter = new DrawCanvas(context);
				painter.update(data);
				painter.clear();
				painter.paint();
			});
		};
		const getDeviceWorkProcessList = async () => {
			// uni.showLoading();
			// let result = await service.getDeviceWorkProcessList(state.deviceSn);
			// state.detail = result.data.data;
			// //会出现页面上便利cavas还没完成的情况，所以延时一下
			// setTimeout(() => {
			//   drawPrice();
			//   uni.hideLoading();
			// }, 300);
		};
		onMounted(async () => {
			getDeviceWorkProcessList();
		});
		const editStrategy = ({ workProcessId }) => {
			uni.navigateTo({
				url: `/packageDevice/strategyEdit?deviceSn=${state.deviceSn}&workProcessId=${workProcessId}`,
			});
		};

		return {
			...toRefs(state),
			editStrategy,
			priceName,
			showPrice,
			canvasWidth,
			canvasHeight,
		};
	},
};
</script>

<style lang="scss" scoped>
.strategy {
	padding: 32rpx 24rpx;
	.box {
		padding: 32rpx 24rpx;
		background: #fff;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
	}
}
.device-data-bottom {
	.tabs {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0;
		.tab {
			text-align: center;
			position: relative;
			padding-bottom: 16rpx;
			width: 33.333%;
			& + .tab {
				border-left: 1px solid rgba(246, 247, 247, 1);
				margin: 0;
			}
			.num {
				font-size: 32rpx;
				line-height: 48rpx;
				font-weight: bold;
				color: #1e312b;
			}
			.name {
				font-size: 24rpx;
				line-height: 40rpx;
				color: rgba(30, 49, 43, 0.6);
			}
		}
	}
	.tabs {
		.tab {
			padding-bottom: 0;
		}
	}
}
.price-chart {
	.title {
		color: rgba(34, 34, 34, 0.65);
		font-size: 28rpx;
	}

	// & + .price-chart {
	//   margin-top: 32rpx;
	// }
}
</style>
