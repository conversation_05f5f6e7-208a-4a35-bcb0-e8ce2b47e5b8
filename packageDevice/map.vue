<template>
  <!-- <web-view src="https://tubiaosit.mingwork.com/#/" @message="getMessage"></web-view> -->
  <web-view id="web-view-1" src="https://tubiaosit.mingwork.com/#/" @message="getMessage"></web-view>
</template>

<script>
export default {
  setup() {

    return {
      
    };
  },
  methods: {
    getMessage(data) {
      let msg = data.detail.params
      uni.setStorageSync("map", msg);
      uni.navigateBack({
            delta:1
      });
    }
  },
};
</script>
