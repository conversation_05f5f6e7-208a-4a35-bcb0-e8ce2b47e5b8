<template>
	<view>
		<uni-popup ref="popup" :safe-area="true" mask-background-color="rgba(20, 20, 20, 0.8)" type="bottom" :mask-click="false" @change="change">
			<view class="fileView">
				<view class="header">
					<text>电站图片</text>
					<view class="closeBtn" @click="$refs.popup.close()">
						<i class="iconfont icon-a-ica-22"></i>
					</view>
				</view>
				<img :src="selectImg" mode="aspectFit" class="image" />
				<view>
					<!-- 	v-model="imageFileList" -->
					<uni-file-picker :sizeType="['compressed']" file-mediatype="image" :sourceType="['camera']" mode="list" return-type="object" @select="selectFile" :auto-upload="false">
						<button class="bt bt-b">拍照</button>
					</uni-file-picker>
				</view>
				<view>
					<!-- v-model="imageFileList" -->
					<uni-file-picker :sizeType="['compressed']" :sourceType="['album']" file-mediatype="image" mode="list" return-type="object" @select="selectFile" :auto-upload="false">
						<button class="bt bt-b">从相册中选择</button>
					</uni-file-picker>
				</view>
				<view>
					<button class="bt bt-b" @click="submit">保存图片</button>
				</view>
				<view class="line"></view>
				<view>
					<button class="bt" @click="$refs.popup.close()">取消</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import upFile from "./upFile.vue";
import { ref, watch } from "vue";
export default {
	components: { upFile },
	setup(props, { emit }) {
		const popup = ref(null);
		const imageFileList = ref([]);
		const openPopup = () => {
			popup.value.open("bottom");
		};

		const colsePopup = () => {
			popup.value.close();
		};
		const change = (e) => {
			emit("hidePopup", e.show);
		};
		const selectImg = ref(null);

		const selectFile = (e) => {
			const tempFilePaths = e.tempFilePaths;
			const imgUrl = tempFilePaths[0];

			const url = "https://ems-api.ssnj.com";
			const token = uni.getStorageSync("accessToken");
			uni.uploadFile({
				//图片上传地址
				url: url + "/file/uploadFile",
				filePath: imgUrl,
				//上传名字，注意与后台接收的参数名一致
				name: "file",
				fileType: "image",
				//设置请求头
				header: {
					"Content-Type": "multipart/form-data",
					Authorization: `Bearer ${token}`,
				},
				//请求成功，后台返回自己服务器上的图片地址
				formData: {
					scene: "stationPic",
				},
				success: (uploadFileRes) => {
					const res = JSON.parse(uploadFileRes.data);
					const {
						code,
						data: { fileVisitUrl },
					} = res;
					if (code === 0) {
						console.log("[ 上传完成 ] >");
						selectImg.value = fileVisitUrl;
						imageFileList.value = [{ name: "xxx.png", extname: "png", url: fileVisitUrl }];
					}
				},
				fail: (error) => {},
			});
		};

		const submit = () => {
			if (imageFileList.value.length == 0) {
				uni.showToast({
					title: "请选择上传图片",
					icon: "error",
				});
				return;
			}
			emit("submit", selectImg.value);
		};

		watch(
			() => props.stationPic,
			(obj) => {
				if (obj) {
					if (obj?.upFile) {
						console.log("[ 监听数据 ] >");
						selectImg.value = obj.url;
						imageFileList.value = [{ name: "xxx.png", extname: "png", url: obj.url }];
					} else {
						selectImg.value = obj?.url || "";
					}
				}
			},
			{ immediate: true, deep: true }
		);

		return {
			popup,
			openPopup,
			imageFileList,
			selectFile,
			selectImg,
			submit,
			colsePopup,
			change,
		};
	},
};
</script>

<style scoped lang="scss">
::ng-deep .uni-file-picker__lists {
	display: none;
}

::v-deep .uni-file-picker__lists {
	display: none;
}
.fileView {
	height: 65vh;
	background-color: #fff;
	border-radius: 24rpx 24rpx 0 0;
	overflow: hidden;
	:deep(.uni-file-picker__lists) {
		display: none;
	}
	.image {
		width: 100%;
		// background-repeat: no-repeat;
		// background-position: center;
		// background-size: cover !important;
	}

	.header {
		color: $uni-secondary-color;
		padding: 28rpx;
		position: relative;
		background: #fff;
		.closeBtn {
			position: absolute;
			right: 28rpx;
			top: 18rpx;
			.iconfont {
				font-size: 48rpx;
				color: $uni-help-color;
			}
		}
	}

	.bt {
		height: 84rpx;
		line-height: 84rpx;
		border-radius: 0;
		// 删除边框
		&::after {
			border: none;
		}
		&.bt-b {
			border-bottom: 1px solid #eee;
		}
	}

	// .line {
	// 	height: 16rpx;
	// 	background-color: #d9d9d9;
	// }
}
:deep(.is-text-box) {
	display: none;
}
</style>
