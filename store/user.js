import service from "@/apiService";

const state = () => {
  return {
    customerInfo: {},
  };
};
const getters = {
};
const mutations = {
  setCustomerInfo(state, customerInfo) {
    state.customerInfo = customerInfo;
  },
};
const actions = {
  async getCustomerInfo({ commit }) {
    console.log('[ store,获取用户信息 ] >',)
    let result = await service.getCurrentUserDetail();
    //保存用户信息
    commit("setCustomerInfo", result.data.data);
    return result
  },
};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
