
const state = () => {
  return {
    supplierList: [],
    orgId: ''
  };
};
const getters = {
  getSupplierList(state) {
    return state.supplierList
  },
  getOrgId(state) {
    return state.orgId
  }
};
const mutations = {

  setData(state, data) {
    state.supplierList = data;
  },
  setOrgId(state, data) {
    state.orgId = data
  }
};
const actions = {

};

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
};
