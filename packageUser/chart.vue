<template>
	<view>
		<view v-if="devices.length">
			<view class="list" v-for="item in devices" :key="item.deviceSn">
				<view class="site-info">
					<view class="header">
						<img src="@/static/logo.svg" class="image" alt="" />
					</view>
					<view class="info">
						<view class="site-name">{{ item.deviceName }}</view>
						<view class="site-detail">
							<view class="flex">
								<i class="iconfont success" :class="getState(item.status).icon"></i><text class="text">{{ getState(item.status).label }}</text>
							</view>
						</view>
					</view>
				</view>
				<view class="chart">
					<view>
						<bar :fromType="fromType" :chartData="item[fromType]" :dateTime="item.dateArray" width="694" />
					</view>
				</view>
			</view>
		</view>
		<empty v-if="devices.length == 0" isPage tips="该企业下没有绑定任何站点" />
	</view>
</template>

<script>
import { reactive, toRefs, onMounted, onBeforeMount } from "vue";
import { onShow } from "@dcloudio/uni-app";
// import bar from "./bar.vue";
import bar from "@/components/bar.vue";
import service from "../apiService/device";
import { getState } from "@/common/setup";
import empty from "../components/empty.vue";

export default {
	props: {},
	components: {
		bar,
		empty,
	},
	setup(props) {
		onShow(async () => {
			const pages = getCurrentPages();
			const options = pages[pages.length - 1].options;
			state.fromType = options.fromType;
			await getSevenStatisticsAll();
		});
		const state = reactive({
			fromType: undefined,
			devices: [],
		});
		const getSevenStatisticsAll = async () => {
			// 获取图表数据
			let result = await service.getSevenStatisticsAll();
			state.devices = [].concat(result.data.data);
		};
		onBeforeMount(() => {});
		return {
			...toRefs(state),
			getState,
		};
	},
};
</script>

<style lang="scss" scoped>
.list {
	padding: 32rpx 28rpx 0 28rpx;
	& + .list {
		border-top: 24rpx solid rgba(30, 49, 43, 0.04);
	}
	.site-info {
		display: flex;
		gap: 16rpx;
		margin-bottom: 24rpx;
		.header {
			width: 84rpx;
			height: 84rpx;
			border-radius: 50%;
			background: #f6f7f7;
			.image {
				width: 56rpx;
				height: 56rpx;
				margin: 16rpx;
				margin-top: 14rpx;
			}
		}
		.info {
			flex: 1;
			.site-name {
				font-size: 28rpx;
				line-height: 44rpx;
				color: #1e312b;
			}
			.site-detail {
				display: flex;
				font-size: 24rpx;
				line-height: 40rpx;
				gap: 28rpx;
				.iconfont {
					width: 40rpx;
					font-size: 40rpx;
					line-height: 40rpx;
					vertical-align: middle;
					margin-right: 8rpx;
					color: $uni-primary;
				}
				.text {
					color: #1e312b;
					opacity: 0.8;
					font-size: 24rpx;
					vertical-align: middle;
				}
			}
		}
	}
}
</style>
