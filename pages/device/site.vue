<template>
	<view class="site">
		<view class="site-header">
			<view class="site-info">
				<view class="header">
					<!-- <img src="@/static/device_default.png" class="image" alt="" /> -->
					<!-- @/static/icon-1.png -->
					<img :src="data?.stationPic || '../../static/newIcon.png'" class="image" mode="aspectFit" />
				</view>
				<view class="info">
					<view class="site-name">
						<text>{{ data.stationName }}</text>
						<img src="@/static/ai.png" class="iconSvg" v-if="data.openVpp && 1 > 2" />
					</view>
					<view class="site-detail">
						<!-- <view class="flex" v-if="data.alarmQuantity > 0">
							<i class="iconfont error icon-ica-dianchi-guzhang"></i>
							<text class="align-middle text">风险[{{ data.alarmQuantity }}条]</text>
						</view> -->
						<view
								class="flex items-center tags"
						>
								<view class="tt">
										运行效率:{{
												data.efficiency >= 50
														? data.efficiency + '%'
														: '-'
										}}
								</view>
						</view>
						<view class="flex">
							<i class="iconfont state-icon" :class="getState(data.status).icon"></i>
							<text class="align-middle text">{{ getState(data.status).label }}</text>
						</view>
						<!-- @click.stop="gotoFailure(data)" -->
					</view>
				</view>
			</view>
			<view class="progress">
				<view class="progress-data">{{ Number(data.soc).toFixed(0) }}<text class="percent">%</text></view>
				<view class="progress-line">
					<view class="active-line" :style="activeLineStyle"></view>
				</view>
			</view>
		</view>
		<view class="site-statistics">
			<!-- <view class="site-statistics-data">
        <view class="num">{{ data.totalCharge || 0 }}</view>
        <view class="name">总充电量(kWh)</view>
      </view>
      <view class="site-statistics-data">
        <view class="num">{{ data.totalDischarge || 0 }}</view>
        <view class="name">总放电量(kWh)</view>
      </view> -->
			<!-- <view class="site-statistics-data">
        <view class="num">{{ data.totalProfit || 0 }}</view>
        <view class="name">总收益(元)</view>
      </view> -->
			<view class="site-statistics-data">
				<view class="num">{{ unitConversion(data.installedCapacity, 1000) || 0 }}</view>
				<view class="name">{{ alternateUnits(data.installedCapacity, 1000) ? "装机容量(MWh)" : "装机容量(kWh)" }}</view>
			</view>
			<view class="site-statistics-data">
				<view class="num">{{ unitConversion(data.totalProfit, 10000) || 0 }}</view>
				<view class="name">
					{{ alternateUnits(data.totalProfit, 10000) ? "总收益(万元)" : "总收益(元)" }}
				</view>
			</view>
			<view class="site-statistics-data">
				<view class="num">{{ data?.createTime ? dayjs(data.createTime).format("YYYY/MM/DD") : "-" }}</view>
				<view class="name">投运日期</view>
			</view>
		</view>
	</view>
</template>
<script>
import { computed, toRefs, ref } from "vue";
import { chargeState } from "../../common/constant";
import { ossStaticResourceUrl } from "@/common/constant";
import { unitConversion, alternateUnits } from "@/common/util";
import dayjs from "dayjs";
export default {
	props: {
		data: {
			type: Object,
			default: () => ({}),
		},
	},
	setup(props) {
		const { data } = toRefs(props);
		const activeLineStyle = computed(() => {
			return {
				width: Number(data.value.soc) + "%",
				// background:
				//   Number(data.value.soc) > 30
				//     ? "#1ECC99"
				//     : Number(data.value.soc) > 10
				//     ? "#FD750B"
				//     : "#FD0B0B",
				background: Number(data.value.soc) > 30 ? "#3EDACD" : Number(data.value.soc) > 10 ? "#FD750B" : "#FF4D4F",
			};
		});
		const getState = (status) => {
			const item = chargeState.find((s) => s.value == status) || {};
			return item;
		};
		const gotoFailure = (data) => {
			uni.navigateTo({
				url: `/packageDevice/failure?deviceSn=${data.deviceSn}&deviceName=${data.deviceName}`,
			});
		};

		return {
			activeLineStyle,
			chargeState,
			getState,
			gotoFailure,
			ossStaticResourceUrl,
			dayjs,
			unitConversion,
			alternateUnits,
		};
	},
};
</script>

<style lang="scss" scoped>
.site {
	padding: 30rpx 22rpx;
	border-radius: 16rpx;
	position: relative;
	z-index: 3;
	// background: linear-gradient(
	//   270deg,
	//   rgba(255, 255, 255, 0.1) 0%,
	//   #ffffff 100%
	// );
	.site-header {
		display: flex;
		// gap: 82rpx;
		padding-bottom: 24rpx;
		align-items: center;
		.site-info {
			flex: 1;
			display: flex;
			gap: 16rpx;
			.header {
				width: 140rpx;
				height: 80rpx;
				// border-radius: 50%;
				background: #f6f7f7;
				.image {
					width: 140rpx;
					height: 80rpx;
					margin: 0;
					// background-repeat: no-repeat;
					// background-position: center;
					// background-size: cover;
				}
			}
			.info {
				flex: 1;
				.site-name {
					font-size: 28rpx;
					line-height: 44rpx;
					color: #1e312b;
					display: flex;
					align-items: center;
					text {
						font-size: 28rpx;
						margin-right: 8rpx;
					}
					.iconSvg {
						width: 40rpx;
						height: 40rpx;
					}
				}
				.site-detail {
					display: flex;
					font-size: 24rpx;
					line-height: 40rpx;
					gap: 20rpx;
					.iconfont {
						width: 40rpx;
						font-size: 40rpx;
						line-height: 42rpx;
						vertical-align: middle;
						margin-right: 8rpx;
						&.state-icon {
							color: $uni-primary;
						}
					}
					.text {
						color: #1e312b;
						opacity: 0.8;
					}
				}
			}
		}
		.progress {
			width: 136rpx;
			text-align: center;
			// margin-top: 10rpx;
			.progress-data {
				font-size: 40rpx;
				line-height: 44rpx;
				color: #1e312b;
				font-weight: bold;
				margin-bottom: 10rpx;
				.percent {
					font-size: 28rpx;
					font-weight: normal;
				}
			}
			.progress-line {
				width: 100%;
				height: 8rpx;
				background: #d8d8d8;
				border-radius: 4rpx;
				.active-line {
					height: 8rpx;
					border-radius: 4rpx;
				}
			}
		}
	}
	.site-statistics {
		display: flex;
		justify-content: space-between;
		text-align: center;
		padding: 24rpx 16rpx;
		background: rgba(246, 247, 247, 0.1);
		border-radius: 16rpx;
		border: 1px solid $uni-border-1;
		// backdrop-filter: blur(10rpx);
		position: relative;
		.site-statistics-data {
			position: relative;
			z-index: 2;
			width: 33.33%;
			flex: 1;
			& + .site-statistics-data {
				border-left: 1px solid $uni-border-1;
			}
		}
		.num {
			font-size: 32rpx;
			line-height: 48rpx;
			color: $uni-main-color;
			font-weight: bold;
		}
		.name {
			font-size: 24rpx;
			line-height: 40rpx;
			color: #1e312b;
			opacity: 0.6;
		}
	}
}
.tags{
	background: rgba(246, 255, 237, 1);padding:0 16rpx;
	.tt{
		font-size: 24rpx;line-height: 44rpx;color: #52c41a;
	}
}
</style>
