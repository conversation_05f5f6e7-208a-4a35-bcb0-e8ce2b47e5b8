<template>
  <view class="data">
    <view class="num" :style="{ color: data.color }">
      <price :data="data.num" :isMoney="isMoney" />
    </view>
    <view class="title">{{ data.title }}</view>
  </view>
</template>

<script>
import { toRefs } from "vue";
import price from "@/components/price.vue";
export default {
  components: { price },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    type: String,
    isMoney: Boolean,
  },
  setup(props) {
    const { data } = toRefs(props);

    return {};
  },
};
</script>

<style lang="scss" scoped>
.data {
  text-align: center;
  .num {
    font-size: 40rpx;
    font-weight: bold;
    line-height: 52rpx;
    color: $uni-main-color;
  }
  .title {
    font-size: 24rpx;
    line-height: 34rpx;
    color: $uni-secondary-color;
  }
}
</style>
