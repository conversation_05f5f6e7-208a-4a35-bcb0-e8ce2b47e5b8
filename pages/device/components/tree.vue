<template>
	<view :class="isChild ? 'is-root' : ''">
		<view class="ul">
			<view class="li" v-for="item in dataList" :key="item.id" :class="selectedId == item.id ? 'li-active' : ''">
				<view class="level-layout" @click="onSelect(item)">
					<text class="name">{{ item.name }}</text>
					<span class="icon"></span>
					<span class="icon-select">
						<span class="icon-round"></span>
					</span>
				</view>
				<tree v-if="item.children && item.children.length" :dataList="item.children" :selectedId="selectedId" :isChild="true" @select="onSelect"></tree>
			</view>
		</view>
	</view>
</template>
<script setup>
import { ref } from "vue";
import tree from "./tree.vue";
const props = defineProps({
	dataList: {
		type: Array,
		default: () => [],
	},
	isChild: {
		type: Boolean,
		default: false,
	},
	selectedId: {
		type: String,
		default: () => "",
	},
});
const emits = defineEmits(["select"]);
const onSelect = (e) => {
	emits("select", e);
};
</script>

<style lang="scss" scoped>
.is-root {
	background: #f5f7f7;
	// padding: 12rpx;
	// padding-bottom: 12rpx;
}
.ul {
	// background: #ffffff;
	padding: 0 12rpx;
	// padding-bottom: 12rpx;

	& > .li {
		border-radius: 8rpx;
		background: #f5f7f7;
		& + .li {
			margin-top: 0;
		}
		.level-layout {
			padding: 0 12rpx;
			font-size: 28rpx;
			line-height: 64rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			padding: 12rpx;
			// margin-bottom: 12rpx;
			position: relative;
			padding-right: 48rpx;
			.name {
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				width: 100%;
			}
			.icon {
				position: absolute;
				right: 36rpx;
				top: 50%;
				transform: translateY(-50%);
				display: block;
				width: 24rpx;
				height: 24rpx;
				border-radius: 9999px;
				border: 2px solid #d9d9d9;
				&::after {
					display: block;
					content: "";
					width: 12rpx;
					height: 12rpx;
					position: absolute;
					left: 6rpx;
					top: 6rpx;
					border-radius: 999px;
					background: transparent;
				}
			}
		}
		&.li-active {
			& > .level-layout {
				// background: $uni-primary;
				color: $uni-primary;
				border-radius: 8rpx;
				.icon {
					border: 2px solid $uni-primary;
					&::after {
						background: $uni-primary;
					}
				}
			}
		}
		& .ul > .li {
			background: #ffffff;
			& + .li {
				margin-top: 12rpx;
			}
			& > .level-layout {
				.icon {
					right: 24rpx;
				}
			}
			& .ul > .li {
				background: #f5f7f7;
				& + .li {
					margin-top: 0;
				}
				& > .level-layout {
					.icon {
						right: 12rpx;
					}
				}
				& .ul > .li {
					background: #ffffff;
					& + .li {
						margin-top: 12rpx;
					}
					& > .level-layout {
						.icon {
							right: 0;
						}
					}
				}
			}
		}
	}
	text {
		line-height: 64rpx;
	}
}
</style>
