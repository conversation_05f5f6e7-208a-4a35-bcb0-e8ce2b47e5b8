<template>
	<view class="container" id="home" :style="homeStyle">
		<!-- <button v-if="showClearTokenButton" type="primary" @click="clearToken">
      清空token
    </button>
    <button
      v-if="showClearTokenButton"
      type="primary"
      @click="test"
      style="margin-top: 10px"
    >
      模拟第一次进来
    </button>
    </button> -->
		<!-- <button type="primary" @click="scanClick">测试点击扫二维码</button> -->
		<view class="change">
			<view class="company-name">{{ selectedSupplierName }}</view>
			<view class="change-btn" @click="openDrawer">
				<img src="@/static/company.svg" class="iconSvg" />
				<text>切换企业</text>
			</view>
		</view>
		<uni-popup ref="supplierDrawer" type="bottom" backgroundColor="#fff">
			<view class="drawer">
				<view class="drawer-header">
					<view class="text-secondary">请选择公司</view>
					<view @click="closeDrawer" class="close-btn">
						<img src="@/static/close.png" class="img" alt="" srcset="" />
					</view>
				</view>
				<view class="drawer-content">
					<tree :dataList="treeData" :selectedId="selectedId" @select="onSelect" />
				</view>
			</view>
		</uni-popup>
		<view class="statisticsBox" @click="showClearToken">
			<view class="statistics">
				<view class="statisticsData">
					<statistics :data="{ num: info.stationQuantity || 0, title: '储能站点(座)' }" />
				</view>
				<view class="statisticsData">
					<statistics :data="{
						num: unitConversion(info.installedCapacity, 1000) || 0,
						title: alternateUnits(info.installedCapacity, 1000) ? '装机容量(MWh)' : '装机容量(kWh)',
					}" />
				</view>
				<view class="statisticsData">
					<statistics :data="{
						num: info.yesterdayProfit || 0,
						title: '昨日收益（元）',
					}" />
				</view>
			</view>
			<view class="statistics">
				<view class="statisticsData">
					<statistics :data="{
						num: unitConversion(info.totalCharge, 1000) || 0,
						title: alternateUnits(info.totalCharge, 1000) ? '总充电量(MWh)' : '总充电量(kWh)',
					}" />
				</view>
				<view class="statisticsData">
					<statistics :data="{
						num: unitConversion(info.totalDischarge, 1000) || 0,
						title: alternateUnits(info.totalDischarge, 1000) ? '总放电量(MWh)' : '总放电量(kWh)',
					}" />
				</view>
				<view class="statisticsData">
					<statistics :data="{
						num: unitConversion(info.totalProfit, 10000) || 0,
						title: alternateUnits(info.totalProfit, 10000) ? '总收益(万元)' : '总收益(元)',
					}" />
				</view>
			</view>
			<view class="statistics" v-if="info.vppStationQuantity > 0 && 1 > 2">
				<view class="statisticsData">
					<statistics :data="{
						num: info.vppStationQuantity || 0,
						title: '虚拟电站(座)',
					}" />
				</view>
				<view class="statisticsData">
					<statistics :data="{
						num: info.settlementCount || 0,
						title: '参与响应次数(次)',
					}" />
				</view>
				<view class="statisticsData">
					<statistics :data="{
						num: unitConversion(info.vppSubsidyAmount, 10000) || 0,
						title: alternateUnits(info.vppSubsidyAmount, 10000) ? '总结算收益(万元)' : '总结算收益(元)',
					}" />
				</view>
			</view>
		</view>

		<view class="siteList">
			<!-- @click="intoDetail(item)" -->
			<navigator v-for="item in siteList" class="siteLi" :key="item.id" hover-class="siteLi-hover"
				:url="`/packageDevice/detail?deviceSn=${item.stationNo}&deviceName=${item.stationName}`">
				<site :data="item" />
			</navigator>
			<empty v-if="!loading && siteList.length == 0" isPage tips="暂无绑定的设备" />
			<uni-load-more v-if="isShowLoadMore && siteList?.length > 3" :status="loadMoreStatus"></uni-load-more>
		</view>
	</view>
</template>

<script>
import { onShow } from "@dcloudio/uni-app";
import statistics from "./statistics.vue";
import site from "./site.vue";
import { reactive, toRefs, onMounted, toRaw, onBeforeMount, ref, getCurrentInstance } from "vue";
import { useLoadMore } from "@/common/setup";
import service from "@/apiService/device";
import store from "@/store/index.js";
import { ossStaticResourceUrl } from "@/common/constant";
import { unitConversion, alternateUnits } from "@/common/util";
import tree from "./components/tree.vue";
import Empty from "@/components/empty.vue";
export default {
	components: {
		statistics,
		site,
		Empty,
		tree,
	},
	onPullDownRefresh: function () {
		this.pullDownRefresh();
		this.getStastics();
	},
	async onLoad() {
		// await this.refreshList();
	},
	setup() {
		onShow(async () => { });
		const getCustomerInfo = async () => {
			if (store.state.user.customerInfo.orgId) {
				//
			} else {
				console.log("[ 1 ] >", 1);
				await store.dispatch("user/getCustomerInfo");
			}
			const usetInfo = store.state.user.customerInfo;
			console.log("[ usetInfo ] >", usetInfo);
			if (usetInfo.roles.includes("operation_staff")) {
				console.log("[ 是运维人员 ]");
			} else {
				console.log("[ 不是运维人员 ]");
				//#ifdef MP-DINGTALK
				dd.removeTabBarItem({
					index: 2, //要删除的tab
					success(res) { },
					fail(res) { },
				});
				//#endif
			}

			uni.setNavigationBarTitle({
				// title: store.state.user.customerInfo.corpName,
				// title: store.state.user.customerInfo.orgName,
				title: "上善能及储能",
			});
		};
		onBeforeMount(async () => { });
		const state = reactive({
			info: {},
			siteList: [],
			supplierList: [],
			isShowLoadMore: false,
			showClearTokenButton: false,
			clickCount: 0,
			lastClickTime: undefined,
		});
		const loading = ref(false);
		const getDataList = async (param, isfresh = false) => {
			loading.value = true;
			state.isShowLoadMore = false;
			uni.showLoading({
				title: "加载中",
			});
			let result = await service.devicePage({
				...param,
				supplierId: selectedId.value,
			});
			setTotalPage(result.data.data.pages);
			if (isfresh) {
				state.siteList = result.data.data.records;
			} else {
				state.siteList = [...state.siteList, ...result.data.data.records];
			}
			state.isShowLoadMore = true;
			uni.hideLoading();
			loading.value = false;
		};

		const getListByPage = async (isfresh = false) => {
			await getDataList(toRaw(page), isfresh);
		};
		const { loadMoreStatus, setTotalPage, page } = useLoadMore(10, getListByPage);
		const pullDownRefresh = async () => {
			page.current = 1;
			await getListByPage(true);
			uni.stopPullDownRefresh(); //刷新完成后停止下拉刷新动效
		};
		const refreshList = async () => {
			await getListByPage(true);
		};
		const intoDetail = (item) => {
			uni.navigateTo({
				url: `/packageDevice/detail?deviceSn=${item.deviceSn}&deviceName=${item.deviceName}`,
			});
		};
		const getStastics = async () => {
			let result = await service.getCustomerStationSummary(selectedId.value);
			state.info = result.data.data;
		};
		const treeData = ref([]);
		const selectedId = ref();
		const selectedSupplierName = ref();
		/**
		 * 获取供应商树形结构,如果已经存在则不重新请求
		 * 1. 如果已经存在供应商树形结构,则直接使用
		 * 2. 如果不存在,则需要获取orgId,并请求供应商树形结构
		 * 3. 将请求结果存储在store中,并设置selectedId
		 */
		const getSupplierList = async () => {
			//
			if (store.state.device.supplierList.length > 0) {
				treeData.value = store.state.device.supplierList;
				return;
			}
			if (store.state.user.customerInfo.orgId) {
			} else {
				// await store.dispatch("user/getCustomerInfo");
			}
			const res = await service.getSubSupplierTree(store.state.user.customerInfo.orgId);
			if (res.data.code === 0) {
				treeData.value = [
					{
						id: store.state.user.customerInfo.orgId,
						name: store.state.user.customerInfo.orgName,
						children: res.data.data,
					},
				];
				store.commit("device/setData", treeData.value);
				selectedId.value = store.state.user.customerInfo.orgId;
				selectedSupplierName.value = store.state.user.customerInfo.orgName;
				store.commit("device/setOrgId", selectedId.value);
			}
		};
		onMounted(async () => {
			//
			page.current = 1;
			await getCustomerInfo();
			await getSupplierList();
			await refreshList();
			await getStastics();
			uni.pageScrollTo({
				scrollTop: 0, // 将滚动位置设置为顶部
				duration: 0, // 滚动到顶部的动画时长，单位为毫秒
			});
			// if (!treeData.value.length) {
			// }
		});
		const showClearToken = () => {
			let now = new Date().getTime();

			if (state.clickCount == 0) {
				state.clickCount = 1;
				state.lastClickTime = now;
			} else if (state.lastClickTime && now - state.lastClickTime < 2500) {
				state.clickCount++;
				state.lastClickTime = now;
			} else {
				state.clickCount = 0;
			}
			if (state.clickCount == 3) {
				state.clickCount = 0;
				state.showClearTokenButton = true;
			}
		};
		const clearToken = () => {
			uni.removeStorageSync("accessToken");
		};
		const test = async () => {
			clearToken();
			page.current = 1;
			state.siteList = [];
			state.info = [];
			await refreshList();
			await getStastics();
		};
		const scanClick = () => {
			uni.scanCode({
				success(res) {
					// debugger;
					// uni.navigateTo({ url: res.path });
				},
			});
		};
		const supplierDrawer = ref();

		const closeDrawer = () => {
			supplierDrawer.value.close();
			homeStyle.value = "overflow:auto;";
		};
		// 关闭窗口
		const homeStyle = ref("");
		const openDrawer = async () => {
			supplierDrawer.value.open();
			homeStyle.value = "overflow:hidden;height:100vh";
			// supplierDrawer.value.open();
		};
		const onSelect = async (e) => {
			selectedSupplierName.value = e.name;
			selectedId.value = e.id;
			page.current = 1;
			await refreshList();
			await getStastics();
			closeDrawer();
			uni.pageScrollTo({
				scrollTop: 0, // 将滚动位置设置为顶部
				duration: 0, // 滚动到顶部的动画时长，单位为毫秒
			});
			// 获取数据
		};
		return {
			...toRefs(state),
			getDataList,
			loadMoreStatus,
			page,
			pullDownRefresh,
			intoDetail,
			refreshList,
			ossStaticResourceUrl,
			getStastics,
			clearToken,
			showClearToken,
			test,
			scanClick,
			unitConversion,
			alternateUnits,
			openDrawer,
			supplierDrawer,
			closeDrawer,
			treeData,
			onSelect,
			selectedId,
			selectedSupplierName,
			store,
			loading,
			homeStyle,
		};
	},
};
</script>

<style lang="scss" scoped>
.container {
	padding: 28rpx 28rpx 0;
	min-height: 100vh;
	background: #f6f7f7;
}

.statisticsBox {
	// padding: 28rpx 24rpx;
	margin-bottom: 28rpx;
	background: #fff;
	// position: fixed;
	// left: 0;
	// top: 0;
	// z-index: 9;
	padding: 10rpx 24rpx;
	border-radius: 16rpx;
}

.statistics {
	display: flex;
	flex-wrap: wrap;
	padding: 24rpx 0;
	row-gap: 32rpx;
	background-size: 100% 100%;
	position: relative;
	z-index: 1;

	&+.statistics {
		border-top: 1px solid $uni-border-1;
	}

	.statisticsData {
		width: 33.33%;
		position: relative;
		z-index: 3;

		&+.statisticsData {
			border-left: 1px solid $uni-border-1;
		}
	}
}

.siteList {
	// margin-top: 304rpx;

	.siteLi {
		border-radius: 16rpx;
		position: relative;
		display: block;
		border: 2rpx solid #fff;
		box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0);
		background: #fff;

		// &:hover {
		//   box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.1);
		// }
		&+.siteLi {
			margin-top: 28rpx;
		}
	}

	.siteLi-hover {
		box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.1);
	}
}

.change {
	display: flex;
	margin-bottom: 24rpx;
	align-items: center;
	justify-content: space-between;

	.company-name {
		flex: 1;
		width: 0;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}

.change-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 188rpx;
	height: 64rpx;
	background: #ffffff;
	border-radius: 36rpx;
	margin-left: auto;

	.iconSvg {
		width: 40rpx;
		height: 40rpx;
	}

	text {
		font-size: 24rpx;
		color: rgba(34, 34, 34, 0.8);
	}
}

.drawer {
	height: calc(100vh);
	background: #fff;

	// border-top-left-radius: 8px;
	// border-top-right-radius: 8px;
	.drawer-header {
		height: 112rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-left: 24rpx;

		.close-btn {
			font-size: 28rpx;
			width: 80rpx;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;

			.img {
				width: 44rpx;
				height: 44rpx;
			}
		}
	}

	.drawer-content {
		height: calc(100vh - 112rpx);
		overflow-y: auto;
		background: #f5f7f7;
		border-radius: 8rpx;
		padding-bottom: 12rpx;
	}
}

.text-secondary {
	color: $uni-secondary-color;
}

:deep(.uni-popup, .bottom) {
	bottom: 0;
}
</style>
