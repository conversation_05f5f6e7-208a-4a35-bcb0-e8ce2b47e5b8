<template>
	<view class="about">
		<view class="user-box">
			<view class="user">
				<view class="user-icon">
					<img src="@/static/logo.svg" class="image" alt="" />
				</view>
				<view class="user-info">
					<view class="name overflow">{{ customerInfo.orgName }}</view>
					<view class="phone">{{ customerInfo.realName }}</view>
				</view>
			</view>
		</view>
		<view class="menu" v-if="false">
			<view class="title">数据图表</view>
			<view class="menu-list">
				<view class="list" v-for="item in datas" :key="item" @click="gotoChart(item)">
					<view class="icon">
						<i :class="['iconfont', item.icon]"></i>
					</view>
					<view class="name">{{ item.label }}</view>
				</view>
			</view>
		</view>
		<view class="more">
			<view class="title">更多</view>
			<view class="more-list">
				<view class="list" @click="createOrder" style="display: none">
					<view class="title">
						<i class="iconfont icon-a-ica-dianchi-guzhangbeifen9"></i>
						<text class="text">创建工单</text>
					</view>
					<view class="icon">
						<i class="iconfont icon-a-ica-dianchi-guzhangbeifen7"></i>
					</view>
				</view>
				<view class="list" @click="goFailure">
					<view class="title">
						<i class="iconfont icon-a-ica-dianchi-guzhangbeifen8"></i>
						<text class="text">故障报警</text>
					</view>
					<view class="icon">
						<i class="iconfont icon-a-ica-dianchi-guzhangbeifen7"></i>
					</view>
				</view>
				<view class="list" v-if="false">
					<view class="title">
						<i class="iconfont icon-a-ica-dianchi-guzhangbeifen10"></i>
						<text class="text">常见问题</text>
					</view>
					<view class="icon">
						<i class="iconfont icon-a-ica-dianchi-guzhangbeifen7"></i>
					</view>
				</view>
				<view class="list" v-if="false">
					<view class="title">
						<i class="iconfont icon-a-ica-dianchi-guzhangbeifen12"></i>
						<text class="text">关于我们</text>
					</view>
					<view class="icon">
						<i class="iconfont icon-a-ica-dianchi-guzhangbeifen7"></i>
					</view>
				</view>
				<view class="list" v-if="false">
					<view class="title">
						<i class="iconfont icon-a-ica-dianchi-guzhangbeifen11"></i>
						<text class="text">设置中心</text>
					</view>
					<view class="icon">
						<i class="iconfont icon-a-ica-dianchi-guzhangbeifen7"></i>
					</view>
				</view>
			</view>
		</view>
		<view class="kf" v-show="false">
			<view>官方热线</view>
			<view>【400-3685-2647】</view>
		</view>
		<view>
			<uni-popup class="uniPopup" ref="popup" type="bottom" :safe-area="true" mask-background-color="rgba(20, 20, 20, 0.8)">
				<view class="popup">
					<view class="header"
						><text>选择站点</text>
						<view class="closeBtn" @click="$refs.popup.close()">
							<i class="iconfont icon-a-ica-22"></i>
						</view>
					</view>
					<view class="devices">
						<view class="list" v-for="item in devices" :key="item.id" @click="gotoCreateOrder(item)">
							<text>{{ item.deviceName }}</text>
						</view>
					</view>
				</view>
			</uni-popup>
		</view>
	</view>
</template>
<script>
import { reactive, toRefs, ref, computed } from "vue";
import service from "@/apiService";
import store from "@/store/index.js";

export default {
	methods: {},
	setup() {
		const state = reactive({
			selectType: undefined,
			devices: [],
			datas: [
				{
					icon: "icon-ica-dianchi-chongdian",
					label: "总充电量",
					fromType: "inChargeArray",
				},
				{
					icon: "icon-ica-dianchi-fangdian",
					label: "总放电量",
					fromType: "disChargeArray",
				},
				{
					icon: "icon-ica-dianchi-yunhangbeifen",
					label: "累计收益",
					fromType: "moneyArray",
				},
				{
					icon: "icon-a-ica-dianchi-yunhangbeifen2",
					label: "故障分布",
					fromType: "malfunctionArray",
				},
			],
		});
		const popup = ref(null);
		const createOrder = async () => {
			// let result = await service.getAllDeviceList();
			// state.devices = result.data.data;
			// state.selectType = "createOrder";
			// popup.value.open("bottom");
		};
		const gotoCreateOrder = ({ id, deviceName, deviceSn }) => {
			if (state.selectType == "createOrder") {
				uni.navigateTo({
					url: `/packageWorkOrder/create?deviceSn=${deviceSn}&deviceName=${deviceName}`,
				});
			} else {
				uni.navigateTo({
					url: `/packageDevice/failure?deviceId=${id}&deviceName=${deviceName}&deviceSn=${deviceSn}`,
				});
			}
			popup.value.close();
		};
		const goFailure = async () => {
			// let result = await service.getAllDeviceList();
			// state.devices = result.data.data;
			// state.selectType = "goFailure";
			// popup.value.open("bottom");
		};
		const customerInfo = computed(() => {
			return store.state.user.customerInfo || {};
		});
		const gotoChart = (item) => {
			uni.navigateTo({
				url: `/packageUser/chart?fromType=${item.fromType}`,
			});
		};
		return {
			...toRefs(state),
			createOrder,
			popup,
			gotoCreateOrder,
			goFailure,
			customerInfo,
			gotoChart,
		};
	},
};
</script>

<style lang="scss" scoped>
.about {
	padding: 28rpx 0;
	height: 100%;
	.user-box {
		padding: 0 28rpx;
	}
	.user {
		padding: 20rpx 24rpx;
		background: rgba(30, 49, 43, 0.04);
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		margin-bottom: 0;
		.user-icon {
			width: 96rpx;
			height: 96rpx;
			background: rgba(30, 49, 43, 0.1);
			display: flex;
			justify-content: center;
			align-items: center;
			text-align: center;
			border-radius: 50%;
			margin-right: 16rpx;
			.image {
				width: 56rpx;
				height: 56rpx;
			}
		}
		.user-info {
			flex: 1;
			.name {
				font-size: 28rpx;
				line-height: 40prx;
				color: $uni-main-color;
			}
			.phone {
				font-size: 24rpx;
				line-height: 34prx;
				color: $uni-secondary-color;
			}
		}
	}
	.menu {
		background: #fff;
		padding: 32rpx 28rpx;
		border-bottom: 24rpx solid rgba(30, 49, 43, 0.04);
		.title {
			font-size: 28rpx;
			color: $uni-secondary-color;
			line-height: 40rpx;
			margin-bottom: 24rpx;
		}
		.menu-list {
			display: flex;
			justify-content: space-between;
			text-align: center;
			.iconfont {
				font-size: 56rpx;
				line-height: 56rpx;
				color: $uni-primary;
			}
			.name {
				font-size: 28rpx;
				line-height: 40rpx;
				caret-color: $uni-main-color;
			}
		}
	}
	.more {
		padding: 32rpx 28rpx;
		.title {
			font-size: 28rpx;
			color: $uni-secondary-color;
			line-height: 40rpx;
			margin-bottom: 8rpx;
		}
		.more-list {
			.list {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx 0;
				border-bottom: 1px solid rgba(34, 34, 34, 0.08);
				.title {
					display: flex;
					margin-bottom: 0;
					line-height: 48rpx;
					.iconfont {
						font-size: 48rpx;
						margin-right: 16rpx;
						color: $uni-main-color;
						vertical-align: middle;
						line-height: 48rpx;
					}
					.text {
						font-size: 28rpx;
						color: $uni-main-color;
						vertical-align: middle;
						line-height: 48rpx;
					}
				}
				.icon {
					font-size: 32rpx;
					.iconfont {
						color: $uni-extra-color;
					}
				}
			}
		}
	}
	.kf {
		font-size: 28rpx;
		line-height: 40rpx;
		color: $uni-help-color;
		text-align: center;
		position: absolute;
		bottom: 22rpx;
		width: 100%;
		view {
			color: $uni-help-color;
		}
	}
}
.popup {
	height: 80vh;
	padding: 24rpx;
	padding-bottom: 40rpx;
	box-shadow: 0px -20px 20px 0px rgba(0, 0, 0, 0.1);
	background: #fff;
	border-top-left-radius: 16rpx;
	border-top-right-radius: 16rpx;
	.header {
		color: $uni-secondary-color;
		padding-bottom: 16rpx;
		position: relative;
		padding-right: 48rpx;
		background: #fff;
		.closeBtn {
			position: absolute;
			right: 0;
			top: 0;
			width: 48rpx;
			height: 48rpx;
			line-height: 48rpx;
			.iconfont {
				font-size: 48rpx;
				color: $uni-help-color;
			}
		}
	}
	.devices {
		height: calc(100% - 48rpx); // 582 -xxx
		overflow-y: scroll;
		.list {
			padding: 32rpx 0;
			color: $uni-main-color;
			& + .list {
				border-top: 1px solid rgba(34, 34, 34, 0.08);
			}
		}
	}
}
</style>
