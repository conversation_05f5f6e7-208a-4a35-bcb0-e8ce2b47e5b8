<template>
	<view class="riak">
		<view class="flex picker">
			<uni-data-select v-model="selectedStation" :localdata="stations" @change="changeStation" placeholder="请选择站点"></uni-data-select>
			<view> </view>
		</view>
		<scroll-view scroll-y @scrolltolower="scrolltolower" @scroll="scroll" class="scorll" :scroll-top="scrollTop">
			<view v-for="item in list" :key="item.id" class="list">
				<InspectItem :data="item" @refresh="refresh" />
			</view>
			<uni-load-more :status="loadMoreStatus" v-if="list.length > 0"></uni-load-more>
			<view class="empty" v-if="list.length == 0">
				<image src="@/static/empty.png" />
				<view class="text">当前暂无巡检记录</view>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
import { onShow } from "@dcloudio/uni-app";
import { onPullDownRefresh, onLoad } from "@dcloudio/uni-app";
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import store from "@/store/index.js";
import service from "@/apiService/device";
import InspectItem from "./InspectItem.vue";
const active = ref(1);
const loadMoreStatus = ref("noMore");

// 告警类型
const list = ref([]);

const config = reactive({
	current: 1,
	size: 10,
});

const isLoading = ref(false);

const stations = ref([]);
const selectedStation = ref();
const getStations = async () => {
	const res = await service.getOrgAndSubOrgStationNameList(store.state.user.customerInfo.orgId || "1726907974555729921");
	stations.value = res.data.data.map((item) => {
		return {
			text: item.stationName,
			value: item.stationNo,
		};
	});
};
// ss
const changeStation = (e) => {
	selectedStation.value = e;
	loadingRefresh(true);
};
const getActivePage = async () => {
	return service.getInspectionDevicePage({ ...config, stationNo: selectedStation.value });
};

const listPage = async (boolean = false) => {
	uni.showLoading({
		title: "加载中",
	});
	// 获取数据
	const {
		data: {
			data: { records, total },
		},
	} = await getActivePage();
	uni.stopPullDownRefresh();
	uni.hideLoading();
	if (boolean) {
		list.value = records;
	} else {
		list.value = list.value.concat(records || []);
	}
	config.total = total;
	if (config.current * config.size >= total) {
		isLoading.value = true;
		loadMoreStatus.value = "noMore";
	} else {
		isLoading.value = false;
		loadMoreStatus.value = "more";
	}
};

const scrolltolower = () => {
	if (!isLoading.value) {
		config.current = config.current + 1;
		listPage();
	}
};

const loadingRefresh = async (boolean = false) => {
	config.current = 1;
	await listPage(boolean);
};

onPullDownRefresh(() => {
	loadingRefresh(true);
});
const refresh = (e) => {
	loadingRefresh(e);
};
onShow(async () => {
	// 只要页面重新进来就会触发onShow
	// const pages = getCurrentPages();
	// const options = pages[pages.length - 1].options;
	// await getStations();
	// loadingRefresh(true);
});
const scrollTop = ref(0);
const oldScrollTop = ref(0);
const scroll = (e) => {
	//
	oldScrollTop.value = e.detail.scrollTop;
};
onMounted(async () => {});
onLoad(async (option) => {
	// 页面第一次从tabbar其他页面跳转进来会触发，否则不会。 详情页进来会触发。
	uni.$on("refreshIndex", async () => {
		// 在这里执行onLoad逻辑

		await getStations();
		await loadingRefresh(true);
		scrollTop.value = oldScrollTop.value;
		nextTick(() => {
			scrollTop.value = 0;
		});
		// setTimeout(() => {
		// scrollTop.value = 0;
		// uni.pageScrollTo({
		// 	scrollTop: 1, // 将滚动位置设置为顶部
		// 	// duration: 300, // 滚动到顶部的动画时长，单位为毫秒
		// 	success: function (e) {
		// 	},
		// 	fail: function (e) {
		// 	},
		// });
		// }, 200);
	});
	await getStations();
	loadingRefresh(true);
});
</script>

<style setup lang="scss">
.riak {
	position: relative;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: rgba(30, 49, 43, 0.04);
	padding-top: 24rpx;

	.riak-tab {
		padding: 24rpx 28rpx;
		display: flex;
		.riak-tab-item {
			height: 56rpx;
			line-height: 40rpx;
			position: relative;
			color: rgba(34, 34, 34, 0.6);
			&:last-child {
				margin-left: 48rpx;
			}
		}

		.active-tab-item {
			color: rgba(111, 190, 206, 1);
			&::after {
				content: "";
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				bottom: 0;
				height: 8rpx;
				background: #6fbece;
				border-radius: 33rpx;
				width: 48rpx;
			}
		}
	}

	.scorll {
		// padding-top: 24rpx;
		//#ifdef MP-DINGTALK
		height: calc(100vh - 180rpx);
		//#endif
		//#ifdef MP-WEIXIN
		height: calc(100vh - 184rpx - env(safe-area-inset-bottom));
		//#endif
	}
	.empty {
		position: absolute;
		top: 20%;
		width: 100%;
		text-align: center;
		.text {
			height: 44rpx;
			font-size: 28rpx;
			font-family: AlibabaPuHuiTi_2_55_Regular;
			color: #000000;
			line-height: 44rpx;
		}
	}
}
.list {
	margin-top: 24rpx;
}
.picker {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 24rpx;
	position: relative;
	z-index: 199;
	.add-btn {
		width: 160rpx;
		height: 56rpx;
		background: #6fbece;
		border-radius: 28rpx;
		color: #fff;
		text-align: center;
		line-height: 56rpx;
	}
	.icon {
		width: 24rpx;
		height: 24rpx;
	}
}
:deep(.uni-select__selector-item) {
	font-size: 28rpx;
	line-height: 64rpx;
	height: 64rpx;
	vertical-align: middle;
	align-items: center;
	color: $uni-base-color;
}
:deep(.uni-select__input-box) {
	max-width: 100%;
}
</style>
