<template>
	<view class="riak1">
		<view class="flex picker">
			<!-- <uni-data-select v-model="selectedStation" :localdata="stations" @change="changeStation" placeholder="请选择站点"></uni-data-select> -->
			<picker @change="changeStation" :value="selectedStation" :range="stations" range-key="label">
				<view class="uni-input">
					<view class="uni-input-text">{{ stations[selectedStation].label }}</view>
					<img src="@/static/down.svg" class="icon" alt="" srcset="" />
				</view>
			</picker>
			<view>
				<!-- <view class="add-btn" @click="addOrder" >添加工单</view> -->
			</view>
		</view>
		<scroll-view scroll-y @scrolltolower="scrolltolower" @scroll="scroll" class="scorll" :scroll-top="scrollTop">
			<view v-for="item in list" :key="item.id" class="list">
				<InspectItem :data="item" @refresh="refresh" />
			</view>
			<uni-load-more :status="loadMoreStatus" v-if="list.length > 0"></uni-load-more>
			<view class="empty" v-if="list.length == 0">
				<image src="@/static/empty.png" />
				<view class="text">当前暂无巡检记录</view>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
import { onShow } from "@dcloudio/uni-app";
import { onPullDownRefresh, onLoad } from "@dcloudio/uni-app";
import { ref, reactive, computed, onMounted, nextTick, watch } from "vue";
import store from "@/store/index.js";
import service from "@/apiService/device";
import InspectItem from "./InspectItem.vue";
const active = ref(1);
const loadMoreStatus = ref("noMore");

// 告警类型
const list = ref([]);

const config = reactive({
	current: 1,
	size: 10,
});

const isLoading = ref(false);

const stations = ref([]);
const selectedStation = ref(0);
const getStations = async () => {
	const res = await service.getOrgAndSubOrgStationNameList(store.state.user.customerInfo.orgId || "1726907974555729921");
	stations.value = res.data.data.map((item) => {
		return {
			label: item.stationName,
			value: item.stationNo,
		};
	});
	stations.value.unshift({
		label: "全部",
		value: "",
	});
};
// ss
const changeStation = (e) => {
	selectedStation.value = e.target.value;
	loadingRefresh(true);
};
const getActivePage = async () => {
	let params = {
		...config,
		stationNo: selectedStation.value > 0 ? stations.value[selectedStation.value].value : "",
	};
	return service.getInspectionDevicePage(params);
};

const listPage = async (boolean = false) => {
	uni.showLoading({
		title: "加载中",
	});
	// 获取数据
	const {
		data: {
			data: { records, total },
		},
	} = await getActivePage();
	uni.stopPullDownRefresh();
	uni.hideLoading();
	if (boolean) {
		list.value = records;
	} else {
		list.value = list.value.concat(records || []);
	}
	config.total = total;
	if (config.current * config.size >= total) {
		isLoading.value = true;
		loadMoreStatus.value = "noMore";
	} else {
		isLoading.value = false;
		loadMoreStatus.value = "more";
	}
};

const scrolltolower = () => {
	if (!isLoading.value) {
		config.current = config.current + 1;
		listPage();
	}
};

const loadingRefresh = async (boolean = false) => {
	config.current = 1;
	await listPage(boolean);
};

onPullDownRefresh(() => {
	loadingRefresh(true);
});
const refresh = (e) => {
	loadingRefresh(e);
};

const scrollTop = ref(0);
const oldScrollTop = ref(0);
const scroll = (e) => {
	//
	oldScrollTop.value = e.detail.scrollTop;
};
onMounted(async () => {
	console.log("[ mounted ] >", 2222222);
});
const props = defineProps({
	activeKey: {
		type: Number,
		default: () => 1,
	},
});
watch(
	() => props.activeKey,
	async (newVal, oldVal) => {
		if (newVal == 3) {
			console.log("[ i-watch ] >");
			await getStations();
			await loadingRefresh(true);
			uni.setStorageSync("refreshInspect", false);
		}
	},
	{ immediate: true, deep: true }
);
onShow(async () => {
	console.log("[ 巡检onshow ] >");
	const isRefresh = uni.getStorageSync("refreshInspect");
	if (isRefresh) {
		await getStations();
		await loadingRefresh(true);
		uni.setStorageSync("refreshInspect", false);
	}
});
onLoad(async (option) => {
	// // 页面第一次从tabbar其他页面跳转进来会触发，否则不会。 详情页进来会触发。
	// uni.$on("refreshIndex", async () => {
	// 	// 在这里执行onLoad逻辑
	// 	await getStations();
	// 	await loadingRefresh(true);
	// 	scrollTop.value = oldScrollTop.value;
	// 	nextTick(() => {
	// 		scrollTop.value = 0;
	// 	});
	// });
	// await getStations();
	// loadingRefresh(true);
});
</script>

<style setup lang="scss">
.riak1 {
	position: relative;
	// height: 100vh;
	display: flex;
	flex-direction: column;
	padding-top: 0;
	.scorll {
		// padding-top: 24rpx;
		//#ifdef MP-DINGTALK
		height: calc(100vh - 180rpx);
		//#endif
		//#ifdef MP-WEIXIN
		height: calc(100vh - 184rpx);
		//#endif
	}

	.empty {
		position: absolute;
		top: 20%;
		width: 100%;
		text-align: center;
		.text {
			height: 44rpx;
			font-size: 28rpx;
			font-family: AlibabaPuHuiTi_2_55_Regular;
			color: #000000;
			line-height: 44rpx;
		}
	}
}
.list {
	margin-top: 24rpx;
	&:first-child {
		margin-top: 0;
	}
}
:deep(.uni-select__selector-item) {
	font-size: 28rpx;
	line-height: 64rpx;
	height: 64rpx;
	vertical-align: middle;
	align-items: center;
	color: $uni-base-color;
}
:deep(.uni-select__input-box) {
	max-width: 100%;
}
.picker {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 28rpx;
	.add-btn {
		min-width: 160rpx;
		height: 56rpx;
		background: #6fbece;
		border-radius: 28rpx;
		color: #fff;
		text-align: center;
		line-height: 56rpx;
	}
	.icon {
		width: 24rpx;
		height: 24rpx;
	}
}
.uni-input {
	min-width: 186rpx;
	max-width: 400rpx;
	height: 56rpx;
	background: #ffffff;
	border-radius: 28rpx;
	color: $uni-secondary-color;
	line-height: 56rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 16rpx;
	font-size: 28rpx;
	.uni-input-text {
		color: $uni-secondary-color;
		font-size: 28rpx;
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		max-width: calc(100% - 36rpx);
	}
	.icon {
	}
}
</style>
