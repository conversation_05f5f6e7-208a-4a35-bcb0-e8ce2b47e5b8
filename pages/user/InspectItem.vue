<template>
	<div class="item">
		<div class="station">
			<div class="info">
				<div>
					<text>{{ data.stationName }}</text> <text class="sm">（{{ data.stationNo }}）</text>
				</div>
				<div class="sm text-secondary">投运日期：{{ data.operationDate }}</div>
			</div>
		</div>
		<div class="container" v-if="data.inspectionDevices?.length">
			<div v-for="(item, index) in data.inspectionDevices" :key="index" class="container-li" @click="onItemClick(item)">
				<div>
					<text>{{ index + 1 }}号机柜</text> <text class="sm">({{ item.deviceSn }})</text>
				</div>
				<div class="sm text-secondary lastInspectTime">上次巡检日期：{{ item.lastInspectionTime ? dayjs(item.lastInspectionTime).format("YYYY/MM/DD") : "-" }}</div>
				<div>
					<dictionary :statusOptions="statusOptions" :value="String(item.inspectionStatus)" :isBackgroundColor="true" />
				</div>
				<div class="addnew" v-if="item?.inspectionStatus === 0" @click.stop="addNew(item)">新增记录</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import dayjs from "dayjs";
import Dictionary from "@/components/dictionary.vue";
const statusOptions = [
	{
		value: "0",
		label: "待巡检",
		color: "#FF4D4F",
		colorOffset: "#EA0C28",
		backGroundColor: "rgba(255, 77, 79, 0.10)",
	},
	{
		value: "1",
		label: "已巡检",
		color: "#52C41A",
		colorOffset: "#5AD8A6",
		backGroundColor: "rgba(246, 255, 237, 1)",
	},
];
const props = defineProps({
	data: {
		type: Object,
		default: () => ({}),
	},
});
const onItemClick = (item) => {
	uni.navigateTo({
		url: "/packageInspection/inspectionDetail?deviceId=" + item.deviceId,
	});
};
const addNew = (item) => {
	uni.navigateTo({
		url: "/packageInspection/add?id=" + props.data.stationId + "&stationName=" + props.data.stationName + "&deviceId=" + item.deviceId + "&containerNo=" + item.containerNo,
	});
};
</script>

<style lang="scss" scoped>
.item {
	background: #ffffff;
	border-radius: 8rpx;
	padding: 24rpx;
	line-height: 44rpx;
}
.sm {
	font-size: 24rpx;
}
.text-secondary {
	color: $uni-secondary-color;
}
.station {
	display: flex;
	align-items: center;
	.pic {
		width: 84rpx;
		height: 84rpx;
	}
	.info {
		flex: 1;
	}
}
.container {
	background: rgba(30, 49, 43, 0.04);
	padding: 8rpx 24rpx;
	margin-top: 16rpx;
}
.container-li {
	padding: 24rpx 0;
	position: relative;
	& + .container-li {
		border-top: 1px solid rgba(34, 34, 34, 0.08);
	}
	.addnew {
		position: absolute;
		right: 0;
		bottom: 24rpx;
		width: 160rpx;
		height: 56rpx;
		line-height: 56rpx;
		color: #6fbece;
		background: #ffffff;
		border-radius: 28rpx;
		text-align: center;
		z-index: 1;
	}
}
.lastInspectTime {
	line-height: 32rpx;
	margin-bottom: 6rpx;
}
</style>
