<template>
	<div>
		<div class="a-row" @click="rowClick($event, data)">
			<view class="a-row-top">
				<view class="top-l">
					<view
						v-if="alarmLeveStatus[data.alarmLevel]?.title"
						class="box"
						:style="{
							color: getColor(data.alarmLevel),
							background: getBgColor(data.alarmLevel),
						}"
						>{{ alarmLeveStatus[data.alarmLevel]?.title }}</view
					>
					<view class="title">{{ data?.alarmDesc }}</view>
				</view>
				<view class="top-r" @click.stop="clearAlarm(data)" v-if="data.alarmStatus == 0">忽略风险</view>
			</view>
			<view class="a-row-b">
				<view class="title-name">设备类型：{{ deviceType[data.deviceType] }}</view>
				<view class="title-name">设备编号：{{ data.deviceSn }}</view>
				<view class="title-name">发生时间：{{ data.alarmTime }}</view>
				<view class="address">
					<image src="@/static/home.png" class="img" />
					<text class="text">{{ data.stationName }} {{ data?.containerNo ? "#" + data.containerNo : "" }}</text>
				</view>
			</view>
		</div>
		<uni-popup ref="alertDialog" type="dialog">
			<uni-popup-dialog :type="'error'" cancelText="关闭" confirmText="同意" title="提示" content="确定清除当前风险？" @cancel="dialogCancel" @confirm="dialogConfirm"></uni-popup-dialog>
		</uni-popup>
	</div>
</template>

<script setup>
import { ref } from "vue";
import service from "@/apiService/device";
const emits = defineEmits(["refresh"]);
const props = defineProps({
	data: {
		type: Object,
		default: () => ({}),
	},
});
const deviceType = {
	SXBLQ: "PCS",
	DUI: "堆",
	CU: "簇",
	YLJ: "液冷机",
	DI: "DI",
	DB: "电表",
	EMSCTRL: "EMS",
	KT: "液冷机",
	FIRE: "消防",
};
const alarmLeveStatus = {
	1: {
		title: "次要",
		color: "rgba(24, 144, 255, 1)",
		background: "rgba(230, 247, 255, 1)",
	},
	2: {
		title: "重要",
		color: "rgba(253, 117, 11, 1)",
		background: "rgba(253, 117, 11, 0.10)",
	},
	3: {
		title: "紧急",
		color: "rgba(255, 77, 79, 1)",
		background: "rgba(255, 77, 79, 0.10)",
	},
};
const alertDialog = ref(null);
const clearAlarmId = ref(null);
const clearAlarmApi = async (alarmId) => {
	const {
		data: { code },
	} = await service.clearAlarm({ alarmId });
	if (code === 0) {
		uni.showToast({ title: "清除成功", duration: 3000 });
		setTimeout(() => {
			// loadingRefresh(true);
			emits("refresh", true);
		}, 300);
	}
};

const clearAlarm = (row) => {
	console.log("[ 1 ] >", 1);
	alertDialog.value.open();
	clearAlarmId.value = row.id;
};
const dialogCancel = () => {
	alertDialog.value.close();
};
const dialogConfirm = () => {
	clearAlarmApi(clearAlarmId.value);
};

const rowClick = (e, row) => {
	console.log("[ 2 ] >", 2);
	uni.navigateTo({
		url: "/pages/user/riakDetail?id=" + row.id,
	});
};
const getColor = (key) => {
	return alarmLeveStatus[key].color;
};
const getBgColor = (key) => {
	return alarmLeveStatus[key].background;
};
</script>

<style lang="scss" scoped>
.a-row {
	padding: 32rpx 28rpx;
	background-color: #fff;
	.a-row-top {
		display: flex;
		justify-content: space-between;
		align-items: center;
		.top-l {
			display: flex;
			align-items: center;
			padding: 4rpx 0;
			.box {
				height: 44rpx;
				font-size: 24rpx;
				font-weight: 400;
				line-height: 44rpx;
				padding: 0 16rpx;
				border-radius: 4rpx;
				background: rgba(255, 77, 79, 0.1);
				margin-right: 26rpx;
			}

			.title {
				height: 48rpx;
				font-size: 32rpx;
				color: #222222;
				line-height: 48rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}

		.top-r {
			font-size: 28rpx;
			line-height: 40rpx;
			padding: 8rpx 24rpx;
			background: #f6f7f7;
			border-radius: 28rpx;
			color: $uni-secondary-color;
		}
	}

	.a-row-b {
		margin-top: 24rpx;
		.title-name {
			height: 44rpx;
			font-size: 28rpx;
			color: $uni-secondary-color;
			line-height: 44rpx;
			margin-bottom: 8rpx;
		}

		.address {
			font-size: 28rpx;
			color: $uni-secondary-color;
			display: flex;
			align-items: center;
			padding-top: 8rpx;
			.img {
				width: 32rpx;
				height: 32rpx;
				opacity: 0.6;
			}
			.text {
				margin-left: 4rpx;
			}
		}
	}

	&:nth-last-child(2) {
		// margin-bottom: 0;
	}
}
</style>
