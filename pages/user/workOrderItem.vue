<template>
	<view>
		<view class="a-row" @click="rowClick($event, data)">
			<view class="a-row-top">
				<view class="top-l">
					<view class="title">{{ data.name }}</view>
				</view>
			</view>
			<view class="a-row-b">
				<view class="title-name">{{ isOperator ? "工单" : "风险" }}编号：{{ data.orderNo }}</view>
				<view class="title-name">跟进人：{{ data.disposeStaffName }}</view>
				<view class="title-name">处理时长：{{ data.processDuration }}小时</view>
				<view class="address">
					<image src="@/static/home.png" class="img" />
					<text class="text">{{ data.stationName }} {{ data?.containerNo ? "#" + data.containerNo : "" }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
const props = defineProps({
	data: {
		type: Object,
		default: () => ({}),
	},
	isOperator: {
		type: Boolean,
		default: false,
	},
});
const rowClick = (e, row) => {
	// 工单跳转详情
	// console.log("[ 工单跳转详情 ] >", row);
	uni.navigateTo({
		url: "/packageInspection/workOrderDetail?id=" + row.id + "&isOperator=" + props.isOperator,
	});
};
</script>

<style lang="scss" scoped>
.a-row {
	padding: 32rpx 28rpx;
	background-color: #fff;
	// margin-bottom: 24rpx;
	.a-row-top {
		display: flex;
		justify-content: space-between;
		align-items: center;
		.top-l {
			width: 100%;
			.box {
				height: 40rpx;
				font-size: 24rpx;
				font-weight: 400;
				line-height: 40rpx;
				padding: 0 16rpx;
				border-radius: 4rpx;
				background: rgba(255, 77, 79, 0.1);
				margin-right: 26rpx;
			}

			.title {
				width: 100%;
				height: 48rpx;
				font-size: 32rpx;
				color: #222222;
				line-height: 48rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}

		.top-r {
			font-size: 28rpx;
			color: $uni-secondary-color;
			line-height: 40rpx;
			padding: 8rpx 24rpx;
			background: #f6f7f7;
			border-radius: 28rpx;
		}
	}

	.a-row-b {
		margin-top: 24rpx;
		.title-name {
			height: 44rpx;
			font-size: 28rpx;
			color: $uni-secondary-color;
			line-height: 44rpx;
			margin-bottom: 8rpx;
		}

		.address {
			font-size: 28rpx;
			color: $uni-secondary-color;
			display: flex;
			align-items: center;
			padding-top: 8rpx;
			.img {
				width: 32rpx;
				height: 32rpx;
				opacity: 0.6;
			}
			.text {
				margin-left: 4rpx;
			}
		}
	}

	&:nth-last-child(2) {
		margin-bottom: 0;
	}
}
</style>
