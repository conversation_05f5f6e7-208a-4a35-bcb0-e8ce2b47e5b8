<template>
	<view class="riak">
		<view class="riak-tab" v-if="isOperator">
			<view class="riak-tab-item" :class="active == item.key ? 'active-tab-item' : ''" v-for="item in tabs"
				:key="item.key" @click="activeClick(item.key)"> {{ item.name }} </view>
		</view>
		<view class="flex picker">
			<picker @change="alarmTypeChange" :value="selectedAlarmType" :range="alarmTypes" range-key="label"
				v-if="isOperator && active == 1">
				<view class="uni-input">
					<view class="uni-input-text">{{ alarmTypes[selectedAlarmType].label }}</view>
					<img src="@/static/down.svg" class="icon" alt="" srcset="" />
				</view>
			</picker>
			<picker @change="orderTypeChange" :value="selectedOrderType" :range="orderTypes" range-key="label"
				v-if="(isOperator && active == 2) || !isOperator">
				<view class="uni-input">
					<view class="uni-input-text">{{ orderTypes[selectedOrderType].label }}</view>
					<img src="@/static/down.svg" class="icon" alt="" srcset="" />
				</view>
			</picker>
			<picker @change="changeStation" :value="selectedStation" :range="stations" range-key="label"
				v-if="isOperator && active == 3">
				<view class="uni-input station">
					<view class="uni-input-text">{{ stations[selectedStation].label }}</view>
					<img src="@/static/down.svg" class="icon" alt="" srcset="" />
				</view>
			</picker>
			<view>
				<!-- <view class="add-btn" v-if="active == 1"></view> -->
				<view class="add-btn" @click="addOrder" v-if="active == 2 && isOperator">添加工单</view>
			</view>
		</view>
		<scroll-view scroll-y @scrolltolower="scrolltolower" class="scorll" :class="{ 'scorll-operator': !isOperator }"
			v-if="isOperator && active == 1">
			<view v-for="item in list" :key="item.id" class="list">
				<AlarmItem :data="item" v-if="isOperator && active == 1" @refresh="refresh" />
				<WorkOrderItem :data="item" :isOperator="isOperator" v-else />
			</view>
			<uni-load-more :status="loadMoreStatus" v-if="list.length > 0"></uni-load-more>
			<view class="empty" v-if="list.length == 0">
				<image src="@/static/empty.png" />
				<view class="text">当前系统安全运行，暂无任何告警</view>
			</view>
		</scroll-view>
		<scroll-view scroll-y @scrolltolower="scrolltolower" class="scorll" :class="{ 'scorll-operator': !isOperator }"
			v-if="(isOperator && active == 2) || !isOperator">
			<view v-for="item in list" :key="item.id" class="list">
				<AlarmItem :data="item" v-if="isOperator && active == 1" @refresh="refresh" />
				<WorkOrderItem :data="item" :isOperator="isOperator" v-else />
			</view>
			<uni-load-more :status="loadMoreStatus" v-if="list.length > 0"></uni-load-more>
			<view class="empty" v-if="list.length == 0">
				<image src="@/static/empty.png" />
				<view class="text">{{ isOperator ? "当前系统安全运行，暂无任何工单" : "当前系统安全运行，暂无任何风险" }}</view>
			</view>
		</scroll-view>
		<scroll-view scroll-y @scrolltolower="scrolltolower" class="scorll" :class="{ 'scorll-operator': !isOperator }"
			v-if="active == 3">
			<view v-for="item in inspectList" :key="item.id" class="list">
				<InspectItem :data="item" v-if="isOperator && active == 3" @refresh="refresh" />
			</view>
			<uni-load-more :status="loadMoreStatus" v-if="inspectList.length > 0"></uni-load-more>
			<view class="empty" v-if="inspectList.length == 0">
				<image src="@/static/empty.png" />
				<view class="text">暂无巡检记录</view>
			</view>
		</scroll-view>
		<!-- <view v-show="active == 3">
			<inspection :activeKey="active" />
		</view> -->
	</view>
</template>

<script setup>
import { onShow } from "@dcloudio/uni-app";
import { onPullDownRefresh, onLoad } from "@dcloudio/uni-app";
import { ref, reactive, computed } from "vue";
import store from "@/store/index.js";
import service from "@/apiService/device";
import WorkOrderItem from "./workOrderItem.vue";
import AlarmItem from "./alarmItem.vue";
import inspection from "./inspection.vue";
import InspectItem from "./InspectItem.vue";

const active = ref(1);
const tab = ref([
	{ name: "告警管理", key: 1 },
	{ name: "工单管理", key: 2 },
	{ name: "巡检管理", key: 3 },
]);
const tabs = computed(() => {
	if (isOperator.value) {
		return tab.value;
	} else {
		return tab.value.slice(0, 2);
	}
});

const activeClick = (key) => {
	active.value = key;
	loadingRefresh(true);
};

const loadMoreStatus = ref("noMore");
const selectedAlarmType = ref(0);
const alarmTypes = [
	{
		label: "当前告警",
		value: "current",
	},
	{
		label: "历史告警",
		value: "history",
	},
];
const alarmTypeChange = (e) => {
	selectedAlarmType.value = e.target.value;
	loadingRefresh(true);
	uni.pageScrollTo({
		scrollTop: 0, // 将滚动位置设置为顶部
		// duration: 300, // 滚动到顶部的动画时长，单位为毫秒
	});
};
const selectedOrderType = ref(0);

const isOperator = computed(() => {
	return store.state.user.customerInfo.roles.includes("operation_staff");
	// return true;
});
const orderTypes = ref([
	{
		label: isOperator.value ? "当前工单" : "当前风险",
		value: 1,
	},
	{
		label: isOperator.value ? "历史工单" : "历史风险",
		value: 2,
	},
]);
const orderTypeChange = (e) => {
	selectedOrderType.value = e.target.value;
	loadingRefresh(true);
	uni.pageScrollTo({
		scrollTop: 0, // 将滚动位置设置为顶部
		// duration: 300, // 滚动到顶部的动画时长，单位为毫秒
	});
};
// 告警类型

const list = ref([]);
const inspectList = ref([]);
const config = reactive({
	current: 1,
	size: 10,
	stationNo: void 0,
});

const isLoading = ref(false);

// 获取风险
const getActivePage = async () => {
	return service.deviceAlarmPage({ ...config, stage: alarmTypes[selectedAlarmType.value].value });
};

const getWorkOrderPage = async (boolean) => {
	let res = await service.getWorkOrderPage({
		...config,
		orderStatus: orderTypes.value[selectedOrderType.value].value,
	});
	uni.stopPullDownRefresh();
	uni.hideLoading();
	if (boolean) {
		list.value = res.data.data.records;
	} else {
		list.value = list.value.concat(res.data.data.records || []);
	}
	config.total = res.data.data.total;
	if (config.current * config.size >= res.data.data.total) {
		isLoading.value = true;
		loadMoreStatus.value = "noMore";
	} else {
		isLoading.value = false;
		loadMoreStatus.value = "more";
	}
};

const listPage = async (boolean = false) => {
	uni.showLoading({
		title: "加载中",
	});
	// 获取数据
	if (isOperator.value) {
		if (active.value == 1) {
			const {
				data: {
					data: { records, total },
				},
			} = await getActivePage();
			uni.stopPullDownRefresh();
			uni.hideLoading();
			if (boolean) {
				list.value = records;
			} else {
				list.value = list.value.concat(records || []);
			}
			config.total = total;
			if (config.current * config.size >= total) {
				isLoading.value = true;
				loadMoreStatus.value = "noMore";
			} else {
				isLoading.value = false;
				loadMoreStatus.value = "more";
			}
		} else if (active.value == 2) {
			await getWorkOrderPage(boolean);
		} else if (active.value == 3) {
			await getInspect(boolean);
		}
	} else {
		await getWorkOrderPage(boolean);
	}
};

// 巡检

const stations = ref([]);
const selectedStation = ref(0);
const getStations = async () => {
	const res = await service.getOrgAndSubOrgStationNameList(store.state.user.customerInfo.orgId || "1726907974555729921");
	stations.value = res.data.data.map((item) => {
		return {
			label: item.stationName,
			value: item.stationNo,
		};
	});
	stations.value.unshift({
		label: "全部站点",
		value: "",
	});
};
const changeStation = (e) => {
	selectedStation.value = e.target.value;
	loadingRefresh(true);
	uni.pageScrollTo({
		scrollTop: 0, // 将滚动位置设置为顶部
		// duration: 300, // 滚动到顶部的动画时长，单位为毫秒
	});
};
const getInspectPage = async () => {
	let params = {
		...config,
		stationNo: selectedStation.value > 0 ? stations.value[selectedStation.value].value : "",
	};
	return service.getInspectionDevicePage(params);
};

const getInspect = async (boolean) => {
	// 获取数据
	const {
		data: {
			data: { records, total },
		},
	} = await getInspectPage();
	uni.stopPullDownRefresh();
	uni.hideLoading();
	if (boolean) {
		inspectList.value = records;
	} else {
		inspectList.value = inspectList.value.concat(records || []);
	}
	config.total = total;
	if (config.current * config.size >= total) {
		isLoading.value = true;
		loadMoreStatus.value = "noMore";
	} else {
		isLoading.value = false;
		loadMoreStatus.value = "more";
	}
};

onShow(async () => {
	const isRefresh = uni.getStorageSync("refreshInspect");
	console.log("[ isRefresh o ] >", isRefresh);
	const detailStationNo = uni.getStorageSync("detailStationNo");
	if (detailStationNo) {
		config.stationNo = detailStationNo;
		active.value = 2;
	}
	await getStations();
	loadingRefresh(true);
});

const scrolltolower = () => {
	if (!isLoading.value) {
		console.log("[ 2 ] >", 2);
		config.current = config.current + 1;
		listPage();
	}
};

const loadingRefresh = (boolean = false) => {
	config.current = 1;
	uni.pageScrollTo({
		scrollTop: 0, // 将滚动位置设置为顶部
		// duration: 300, // 滚动到顶部的动画时长，单位为毫秒
		success: function (e) {
			console.log("[ 4 ] >", 4);
		},
		fail: function (e) {
			console.log("[ 5 ] >", 5);
		},
	});
	listPage(boolean);
};

onPullDownRefresh(() => {
	loadingRefresh(true);
});
const refresh = (e) => {
	loadingRefresh(e);
};
onLoad((option) => {
	const { stationNo, isOperator } = option;
	if (isOperator) {
		active.value = 2;
	}
	if (stationNo) {
		config.stationNo = stationNo;
	}
	// loadingRefresh(true);
});
const addOrder = () => {
	uni.navigateTo({
		url: "/packageInspection/addOrder?id=1",
	});
};
</script>

<style lang="scss" scoped>
.riak {
	position: relative;
	height: 100vh;
	display: flex;
	flex-direction: column;
	background-color: rgba(30, 49, 43, 0.04);

	.riak-tab {
		padding: 24rpx 28rpx 0;
		display: flex;

		.riak-tab-item {
			height: 56rpx;
			line-height: 40rpx;
			position: relative;
			color: rgba(34, 34, 34, 0.6);

			&+.riak-tab-item {
				margin-left: 48rpx;
			}
		}

		.active-tab-item {
			color: rgba(111, 190, 206, 1);

			&::after {
				content: "";
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				bottom: 0;
				height: 8rpx;
				background: #6fbece;
				border-radius: 33rpx;
				width: 48rpx;
			}
		}
	}

	.scorll {
		// padding-top: 24rpx;
		//#ifdef MP-DINGTALK
		height: calc(100vh - 180rpx);
		//#endif
		//#ifdef MP-WEIXIN
		height: calc(100vh - 184rpx);
		//#endif
	}

	.scorll-operator {
		height: calc(100vh - 96rpx);
	}

	.empty {
		position: absolute;
		top: 20%;
		width: 100%;
		text-align: center;

		.text {
			height: 44rpx;
			font-size: 28rpx;
			font-family: AlibabaPuHuiTi_2_55_Regular;
			color: #000000;
			line-height: 44rpx;
		}
	}
}

.uni-input {
	width: 186rpx;
	height: 56rpx;
	background: #ffffff;
	border-radius: 28rpx;
	color: $uni-secondary-color;
	line-height: 56rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 16rpx;
	font-size: 28rpx;

	.uni-input-text {
		color: $uni-secondary-color;
		font-size: 28rpx;
	}

	&.station {
		min-width: 186rpx;
		max-width: 440rpx;
		width: auto;

		.uni-input-text {
			flex: 1;
			padding-right: 12rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
}

.picker {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 28rpx;

	.add-btn {
		width: 160rpx;
		height: 56rpx;
		background: #6fbece;
		border-radius: 28rpx;
		color: #fff;
		text-align: center;
		line-height: 56rpx;
	}

	.icon {
		width: 24rpx;
		height: 24rpx;
	}
}

.list {
	margin-top: 24rpx;

	&:first-child {
		margin-top: 0;
	}
}
</style>

<!-- ❌❌❌❌❌ 复制到D页面的时候。需要把stationNo放出来 -->
