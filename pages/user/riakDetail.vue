<template>
	<view class="riak-detail">
		<view class="detail-content">
			<view class="main">
				<view class="main-box">
					<view class="title">
						<image src="@/static/jingao.png" />
						<text>{{ dataDetail?.alarmDesc || "-" }}</text>
					</view>
					<view class="item-box">
						<view class="item-label">风险状态</view>
						<view class="item-content">
							<view
								class="redius"
								:style="{
									background: getStatus('backGroundColor', dataDetail?.alarmStatus) ? getStatus('backGroundColor', dataDetail?.alarmStatus) : null,
								}"></view>
							<text>{{ getStatus("label", dataDetail?.alarmStatus) ? getStatus("label", dataDetail?.alarmStatus) : "-" }}</text>
						</view>
					</view>
					<view class="item-box">
						<view class="item-label">风险级别</view>
						<view class="item-content">
							<view
								class="level"
								:style="{
									color: getColor(dataDetail?.alarmLevel),
									background: getBgColor(dataDetail?.alarmLevel),
								}"
								>{{ dataDetail?.alarmLevel ? alarmLeveStatus[dataDetail.alarmLevel].title : "-" }}</view
							>
						</view>
					</view>
					<view class="item-box">
						<view class="item-label">站点名称</view>
						<view class="item-content">
							{{ dataDetail?.stationName || "-" }}
						</view>
					</view>
					<view class="item-box">
						<view class="item-label">站点编号</view>
						<view class="item-content">
							{{ dataDetail?.stationNo || "-" }}
						</view>
					</view>

					<view class="item-box">
						<view class="item-label">机柜编号</view>
						<view class="item-content">
							{{ dataDetail?.containerNo || "-" }}
						</view>
					</view>

					<view class="item-box">
						<view class="item-label">设备类型</view>
						<view class="item-content">
							{{ dataDetail?.deviceType ? deviceType[dataDetail.deviceType] : "-" }}
						</view>
					</view>

					<view class="item-box">
						<view class="item-label">设备编号</view>
						<view class="item-content">
							{{ dataDetail?.deviceSn || "-" }}
						</view>
					</view>

					<view class="item-box">
						<view class="item-label">发生时间</view>
						<view class="item-content">
							{{ dataDetail?.alarmTime || "-" }}
						</view>
					</view>

					<view class="item-box" v-if="dataDetail?.alarmStatus != 0">
						<view class="item-label">处理时间</view>
						<view class="item-content">
							{{ dataDetail?.disposeTime || "-" }}
						</view>
					</view>

					<view class="item-box" v-if="dataDetail?.alarmStatus != 0">
						<view class="item-label">处理人</view>
						<view class="item-content">
							{{ dataDetail?.disposeStaffName ? dataDetail?.disposeStaffName : dataDetail?.disposeAction == 1 ? "系统操作" : "-" }}
						</view>
					</view>

					<view class="item-box" v-if="dataDetail?.alarmStatus != 0">
						<view class="item-label">处理操作</view>
						<view class="item-content">
							{{ dataDetail?.disposeAction ? (dataDetail?.disposeAction == 1 ? "转为工单" : "忽略告警") : "-" }}
						</view>
					</view>
				</view>
				<view class="bt-box">
					<view class="bt bt-primary" @click="lookOrder" v-if="dataDetail?.workOrderId">查看工单</view>
					<view class="bt bt-primary" @click="alarmTransfer" v-if="dataDetail?.alarmStatus == 0">转为工单</view>
					<view class="bt" @click="clearAlarm" v-if="dataDetail?.alarmStatus == 0">忽略风险</view>
				</view>
			</view>
		</view>
		<uni-popup ref="alertDialog" type="dialog" @maskClick="maskClick($event)">
			<uni-popup-dialog :type="'error'" cancelText="关闭" confirmText="同意" title="提示" content="确定清除当前风险？" @confirm="dialogConfirm"></uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import service from "@/apiService/device";

// 告警类型
const deviceType = {
	SXBLQ: "PCS",
	DUI: "堆",
	CU: "簇",
	YLJ: "液冷机",
	DI: "DI",
	DB: "电表",
	EMSCTRL: "EMS",
	KT: "液冷机",
	FIRE: "消防",
};

const alertDialog = ref(null);

const alarmLeveStatus = {
	1: {
		title: "次要",
		color: "rgba(24, 144, 255, 1)",
		background: "rgba(230, 247, 255, 1)",
	},
	2: {
		title: "重要",
		color: "rgba(253, 117, 11, 1)",
		background: "rgba(253, 117, 11, 0.10)",
	},
	3: {
		title: "紧急",
		color: "rgba(255, 77, 79, 1)",
		background: "rgba(255, 77, 79, 0.10)",
	},
};
const getColor = (key) => {
	return alarmLeveStatus[key].color;
};
const getBgColor = (key) => {
	return alarmLeveStatus[key].background;
};
const alarmStatusList = {
	0: {
		label: "待处理",
		backGroundColor: "#EA0C28",
	},
	1: {
		label: "已恢复",
		backGroundColor: "#4B82FF",
	},
	2: {
		label: "已忽略",
		backGroundColor: "##D9D9D9",
	},
	3: {
		label: "已报障",
		backGroundColor: "#8AD0FF",
	},
};

const getStatus = (key, item) => {
	if (!dataDetail.value) return false;
	console.log("[ item,key ] >", item, key);
	return alarmStatusList[item][key];
};

const time = computed(() => {
	return dataDetail?.alarmStatus ? (dataDetail.alarmStatus == 1 ? dataDetail.recoverTime : dataDetail.disposeTime) : "-";
});

const dataDetail = ref();

const getDetail = async (alarmId) => {
	const {
		data: { data, code },
	} = await service.getDetail({ alarmId });
	if (code === 0) {
		dataDetail.value = data;
	}
};
const alarmTransfer = async () => {
	const res = await service.alarmTransfer({ alarmId: dataDetail.value.id });
	if (res.data.data) {
		uni.showToast({ title: "操作成功" });
		// uni.reLaunch({ url: "/pages/user/riakPage" });
		uni.redirectTo({
			url: "/packageInspection/workOrderDetail?id=" + res.data.data + "&isOperator=" + true,
		});
	} else {
		uni.showToast({
			icon: "error",
			title: "操作失败",
		});
	}
};
const clearAlarmApi = async (alarmId) => {
	const {
		data: { code },
	} = await service.clearAlarm({ alarmId });
	if (code === 0) {
		uni.showToast({ title: "该风险已清除" });
		uni.reLaunch({
			url: "/pages/user/riakPage",
		});
	}
};

const clearAlarm = () => {
	alertDialog.value.open();
	// clearAlarmApi(dataDetail.value.id);
};
const maskClick = (e) => {
	alertDialog.value.close();
	// clearAlarmApi(dataDetail.value.id);
};

const dialogConfirm = () => {
	clearAlarmApi(dataDetail.value.id);
};
const lookOrder = () => {
	uni.navigateTo({
		url: "/packageInspection/workOrderDetail?id=" + dataDetail.value?.workOrderId + "&isOperator=" + true,
	});
};
onLoad((option) => {
	const { id } = option;
	id && getDetail(id);
});
</script>

<style scoped lang="scss">
.riak-detail {
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	// background-color: rgba(30, 49, 43, 0.04);
	.detail-content {
		height: 100%;
		padding-top: 24rpx;
		box-sizing: border-box;
		border-top: 24rpx solid rgba(30, 49, 43, 0.04);
		.main {
			height: calc(100% - 24rpx);
			background-color: #fff;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			.bt-box {
				margin-bottom: 72rpx;
				.bt {
					width: 480rpx;
					height: 92rpx;
					background: #f6f7f7;
					border-radius: 16rpx;
					font-size: 28rpx;
					color: #ff4d4f;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 0 auto;
					&.bt {
						margin-top: 24rpx;
					}
					&.bt-primary {
						background: #6fbece;
						color: #fff;
					}
				}
			}

			.main-box {
				flex: 1;
				padding: 0 28rpx;
				.title {
					display: flex;
					align-items: center;
					margin-top: 40rpx;
					margin-bottom: 38rpx;
					image {
						width: 18px;
						height: 18px;
						margin-left: 6rpx;
					}
					text {
						font-size: 28rpx;
						color: #222222;
						margin-left: 22rpx;
					}
				}

				.item-box {
					display: flex;
					align-items: center;
					margin-bottom: 24rpx;
					.item-label {
						width: 136rpx;
						height: 44rpx;
						font-size: 28rpx;
						color: rgba(34, 34, 34, 0.6);
						line-height: 44rpx;
					}
					.item-content {
						font-size: 28rpx;
						color: #595959;
						display: flex;
						align-items: center;
						.redius {
							display: inline-block;
							width: 20rpx;
							height: 20rpx;
							border-radius: 50%;
						}

						text {
							color: #595959;
							margin-left: 12rpx;
						}

						.level {
							width: 80rpx;
							height: 44rpx;
							border-radius: 4rpx;
							font-size: 24rpx;
							display: flex;
							align-items: center;
							justify-content: center;
						}
					}

					&:last-child {
						margin-bottom: 0;
					}
				}
			}
		}
	}
}
</style>
