<template>
  <view>
    <button class="primary large" @click="getin">重新进入</button>
    <empty isPage tips="暂无绑定的设备" />
  </view>
</template>
<script>
import Empty from "@/components/empty.vue";

export default {
  components: { Empty },
  setup() {
    const getin = () => {
      uni.reLaunch({
        url: "/pages/device/index",
      });
    };
    return { getin };
  },
};
</script>
<style scoped>
button {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 320rpx;
  transform: translate(-50%, -50%);
}
</style>
