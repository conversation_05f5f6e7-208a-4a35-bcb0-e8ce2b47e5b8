<template>
  <view class="my-order">
    <view class="tab-filter">
      <tabs-filter
        :selectedValue="orderStatus"
        :options="worksheetStatus"
        @change="handleStatusFilter"
      />
    </view>
    <view class="orders" v-if="data.length > 0">
      <view class="order" v-for="item in data" :key="item.orderId">
        <order-item :data="item"></order-item>
      </view>
      <uni-load-more
        v-if="isShowLoadMore & (data.length > 10)"
        :status="loadMoreStatus"
      ></uni-load-more>
    </view>
    <empty v-if="!listLoading && data.length == 0" isPage :tips="emptyText" />
  </view>
</template>
<script>
import {
  defineComponent,
  toRaw,
  ref,
  onBeforeMount,
  reactive,
  toRefs,
  computed,
} from "vue";
import service from "@/apiService/worksheet";
import orderItem from "@/pages/workOrder/item.vue";
import { worksheetStatus, ossStaticResourceUrl } from "@/common/constant";
import { useLoadMore } from "@/common/setup";
import tabsFilter from "@/components/tabsFilter";
import Empty from "@/components/empty.vue";
import { onShow } from "@dcloudio/uni-app";

export default {
  components: { Empty, tabsFilter, orderItem },
  onPullDownRefresh: function () {
    this.pullDownRefresh();
  },

  setup() {
    const data = ref([]),
      isShowLoadMore = ref(false);
    const state = reactive({
      listLoading: false,
    });
    const emptyText = computed(() => {
      return orderStatus.value == "processing"
        ? "当前暂无处理中工单"
        : orderStatus.value == "finished"
        ? "当前暂无已完成工单"
        : "当前暂无工单";
    });
    onShow(() => {
      getOrderListByPage(true);
    });
    const getOrderList = async (param, isfresh = false) => {
      isShowLoadMore.value = false;
      uni.showLoading({
        title: "加载中",
      });
      state.listLoading = true;
      let result = await service.worksheetList(param);
      if (isfresh) {
        data.value = result.data.data.records;
      } else {
        data.value = [...data.value, ...result.data.data.records];
      }
      setTotalPage(result.data.data.pages);
      isShowLoadMore.value = true;
      state.listLoading = false;
      uni.hideLoading();
    };
    const orderStatus = ref("processing");

    const getOrderListByPage = async (isfresh = false) => {
      let param = { ...toRaw(page) };
      if (orderStatus.value != "all") {
        param.status = orderStatus.value;
      }
      await getOrderList(param, isfresh);
    };
    const { loadMoreStatus, setTotalPage, page } = useLoadMore(
      10,
      getOrderListByPage
    );

    const pullDownRefresh = async () => {
      page.current = 1;
      refreshList();
      uni.stopPullDownRefresh(); //刷新完成后停止下拉刷新动效
    };

    const refreshList = async () => {
      let param = { ...toRaw(page) };
      if (orderStatus.value != "all") {
        param.status = orderStatus.value;
      }
      await getOrderList(param, true);
    };
    const handleStatusFilter = async (val) => {
      page.current = 1;
      orderStatus.value = val;
      refreshList();
    };
    onBeforeMount(async () => {});
    return {
      ...toRefs(state),
      orderStatus,
      data,
      worksheetStatus,
      handleStatusFilter,
      refreshList,
      loadMoreStatus,
      isShowLoadMore,
      pullDownRefresh,
      ossStaticResourceUrl,
      emptyText,
    };
  },
};
</script>
<style lang="scss" scoped>
.my-order {
  height: 100vh;
  background-color: rgba(30, 49, 43, 0.04);
  .order {
    margin-bottom: 24rpx;
  }
  :deep(.tab) {
    font-size: 28rpx;
  }
}
</style>
