<template>
  <view class="box">
    <view class="top">
      <view class="flex-1 title">
        <view class="icon"
          ><i class="iconfont icon-ica-dianchi-guzhang"></i
        ></view>
        <view class="text"
          ><dictionary :statusOptions="worksheetTypes" :value="data.type"
        /></view>
      </view>
    </view>
    <view class="mid">
      <view class="title">{{ data.content }}</view>
      <view class="time">时间：{{ data.createTime }}</view>
    </view>
    <view class="bot">
      <view class="name">
        <i class="iconfont icon-ica-shouye-1"></i>
        <text class="text overflow">{{ data.deviceName }}</text>
      </view>
      <view class="create-btn" @click="gotoDetail">工单详情</view>
    </view>
  </view>
</template>
<script>
import { toRefs } from "vue";
import dictionary from "@/components/dictionary.vue";
import { worksheetTypes } from "@/common/constant.js";

export default {
  components: { dictionary },
  props: {
    data: Object,
  },
  setup(props, { emit }) {
    const { data } = toRefs(props);
    const ignore = () => {
      emit("finish");
    };
    const gotoDetail = () => {
      uni.navigateTo({
        url: "/packageWorkOrder/detail?id=" + data.value.id,
      });
    };
    return {
      ignore,
      gotoDetail,
      worksheetTypes,
    };
  },
};
</script>

<style lang="scss" scoped>
.box {
  padding: 32rpx 28rpx;
  background: #fff;
}
.top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28rpx;
  .title {
    display: flex;
    align-items: center;
    line-height: 48rpx;
  }
  .icon {
    width: 48rpx;
    height: 48rpx;
    text-align: center;
    line-height: 48rpx;
    border-radius: 24rpx;
    background: #f6f7f7;
    margin-right: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .iconfont {
    font-size: 36rpx;
    color: $uni-error;
  }
  .text {
    font-size: 28rpx;
    line-height: 48rpx;
    color: $uni-secondary-color;
  }
  .ignore {
    width: 112rpx;
    height: 56rpx;
    background: #f6f7f7;
    border-radius: 28rpx;
    line-height: 56rpx;
    font-size: 28rpx;
    text-align: center;
    color: $uni-secondary-color;
  }
}
.mid {
  margin-bottom: 24rpx;
  .title {
    font-size: 32rpx;
    line-height: 48rpx;
    color: $uni-main-color;
    margin-bottom: 8rpx;
    font-weight: 600;
    word-break: break-all;
  }
  .time {
    font-size: 28prx;
    color: $uni-secondary-color;
    line-height: 44rpx;
  }
}
.bot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .name {
    display: flex;
    align-items: center;
    flex: 1;
    padding-right: 24rpx;
    width: 0;
    .iconfont {
      font-size: 32rpx;
      line-height: 32rpx;
      margin-right: 8rpx;
      color: #ccd4d8;
    }
    .text {
      font-size: 28rpx;
      line-height: 40rpx;
      color: $uni-secondary-color;
    }
  }
  .create-btn {
    font-size: 28rpx;
    line-height: 56rpx;
    color: #fff;
    width: 156rpx;
    height: 56rpx;
    text-align: center;
    background: $uni-primary;
    border-radius: 8rpx;
  }
}
</style>
