<template>
	<view class="bind">
		<view style="padding: 80rpx 0; margin-bottom: 16rpx; text-align: center">
			<view>
				<img style="width: 48rpx; height: 48rpx; margin-right: 8rpx; vertical-align: middle" src="@/static/logo.svg" alt="" srcset="" />
				<text style="font-size: 36rpx; font-weight: 500; vertical-align: middle">储能设备绑定</text></view
			>
			<!-- <view style="font-size: 24rpx; color: #9b9b9b; line-height: 40rpx"
        >设备型号：{{ device || "-" }}</view
      > -->
		</view>
		<div style="padding: 0 28rpx">
			<uni-forms ref="form" v-model="formState" style="background: #ffffff" err-show-type="toast">
				<view class="from-label-title">客户信息</view>
				<uni-forms-item>
					<template v-slot:label>
						<div class="form-label">
							<!-- <text class="iconfont icon-ica-3"></text> -->
							<text>企业名称</text>
						</div>
					</template>
					<text style="opacity: 80%">{{ orgName }}</text>
				</uni-forms-item>
				<uni-forms-item>
					<template v-slot:label>
						<div class="form-label">
							<!-- <text class="iconfont icon-ica-4"></text> -->
							<text>联系人</text>
						</div>
					</template>
					<text style="opacity: 80%">{{ userName }}</text>
				</uni-forms-item>
				<!-- <uni-forms-item>
          <template v-slot:label>
            <div class="form-label">
              <text class="iconfont icon-ica-6beifen"></text
              ><text>联系手机</text>
            </div>
          </template>
          <text style="opacity: 80%">{{ phone || "未授权获取" }}</text>
        </uni-forms-item> -->
				<view class="from-label-title from-item">站点信息</view>
				<view class="form-list">
					<uni-forms-item name="deviceSn">
						<template v-slot:label>
							<div class="form-label">
								<!-- <text class="iconfont icon-jinghao"></text> -->
								<text>站点编号</text>
							</div>
						</template>
						<uni-easyinput
							@input="inputChange('deviceSn')"
							trim="all"
							v-model="formState.deviceSn"
							placeholder="请输入站点编号"
							:styles="{ borderColor: 'transparent' }"
							:maxlength="20"
							:inputBorder="false"
							primaryColor="#1ECC99"
							placeholderStyle="font-size: 28rpx; color: #cfcfcf;margin-left:-8rpx"
							:clearSize="22"></uni-easyinput>
					</uni-forms-item>
				</view>
				<view class="form-list">
					<uni-forms-item name="stationName">
						<template v-slot:label>
							<div class="form-label">
								<!-- <text class="iconfont icon-ica-6"></text> -->
								<text>站点名称</text>
							</div>
						</template>
						<uni-easyinput
							trim="all"
							v-model="formState.stationName"
							placeholder="请输入站点名称"
							:styles="{ borderColor: 'transparent' }"
							:maxlength="20"
							:inputBorder="false"
							primaryColor="#1ECC99"
							placeholderStyle="font-size: 28rpx; color: #cfcfcf;margin-left:-8rpx"
							:clearSize="22"
							@input="inputChange('stationName')"></uni-easyinput>
					</uni-forms-item>
				</view>
				<view class="form-list">
					<uni-forms-item name="areaId">
						<template v-slot:label>
							<div class="form-label">
								<!-- <text class="iconfont icon-a-ica-5beifen2"></text> -->
								<text>站点地区</text>
							</div>
						</template>
						<uni-data-select v-model="formState.areaId" :localdata="areas" placeholder="请选择地区" :clear="false" :inputBorder="false" @change="handleAreaChange"></uni-data-select>
					</uni-forms-item>
				</view>
				<view class="form-list">
					<uni-forms-item name="address">
						<template v-slot:label>
							<div class="form-label">
								<!-- <text class="iconfont icon-ica-5"></text> -->
								<text>站点地址</text>
							</div>
						</template>
						<uni-easyinput
							trim="all"
							v-model="formState.address"
							placeholder="请输入站点地址"
							:styles="{ padding: 0 }"
							:maxlength="30"
							:inputBorder="false"
							primaryColor="#1ECC99"
							:clearSize="22"
							placeholderStyle="font-size: 28rpx; color: #cfcfcf;margin-left:-8rpx"
							suffixIcon="location"
							@iconClick="iconClick"
							disabled></uni-easyinput>
					</uni-forms-item>
				</view>
				<view class="form-list">
					<uni-forms-item name="electricType">
						<template v-slot:label>
							<div class="form-label">
								<!-- <text class="iconfont icon-a-ica-shouye-1beifen6"></text> -->
								<text>用电类型</text>
							</div>
						</template>
						<uni-data-select v-model="formState.electricType" :localdata="electricTypes" placeholder="请选择用电类型" :clear="false" :inputBorder="false" @change="handleAreaChange"></uni-data-select>
					</uni-forms-item>
				</view>
				<view class="form-list">
					<uni-forms-item name="containerQuantity">
						<template v-slot:label>
							<div class="form-label">
								<!-- <text class="iconfont icon-a-ica-shouye-1beifen6"></text> -->
								<text>选配台数</text>
							</div>
						</template>
						<uni-data-select
							v-model="formState.containerQuantity"
							:localdata="containerQuantity"
							placeholder="请选择台数"
							:clear="false"
							:inputBorder="false"
							@change="quantityChange"></uni-data-select>
					</uni-forms-item>
				</view>
				<view class="form-list">
					<uni-forms-item name="containerQuantity">
						<template v-slot:label>
							<div class="form-label">
								<!-- <text class="iconfont icon-a-ica-shouye-1beifen6"></text> -->
								<text>装机容量</text>
							</div>
						</template>
						<uni-easyinput
							v-model="containerQuantityNum"
							trim="all"
							:inputBorder="false"
							primaryColor="#1ECC99"
							placeholderStyle="font-size: 28rpx; color: #cfcfcf;margin-left:-8rpx"
							:clearSize="22"
							disabled></uni-easyinput>
					</uni-forms-item>
				</view>
				<!-- <view class="form-list">
          <uni-forms-item name="voltage">
            <template v-slot:label>
              <div class="form-label">
                <text class="iconfont icon-a-ica-shouye-1beifen7"></text>
                <text>选择电压</text>
              </div>
            </template>
            <uni-data-select
              v-model="formState.voltage"
              :localdata="voltages"
              placeholder="请选择电压"
              :clear="false"
              :inputBorder="false"
              @change="handleAreaChange"
            ></uni-data-select>
          </uni-forms-item>
        </view> -->
			</uni-forms>
			<!-- <button @click="cancelAuth">取消授权（测试用）</button> -->
		</div>
		<!-- :disabled="submitDisabled" -->
		<button class="primary large" :disabled="submitDisabled" @click="submit">立即绑定</button>
	</view>
</template>
<script>
import { reactive, ref } from "vue";
import service from "@/apiService";
import { dingTalkClientId } from "@/common/env";
import store from "@/store/index.js";
//#ifdef MP-DINGTALK
import { openAuthMiniApp, disposeAuthData } from "dingtalk-design-libs/biz/openAuthMiniApp";
// import dd from "dingtalk-jsapi";
//#endif
export default {
	data() {
		return {
			orgName: "-",
			userName: "-",
			phone: "",
			device: "",
			userId: "",
			clickedSubmit: false,
			areas: [],
			electricTypes: [
				{
					text: "大工业用电",
					value: 1,
				},
				{
					text: "一般工商业用电",
					value: 2,
				},
			],
			voltages: [
				{
					text: "10kV",
					value: "10kV",
				},
			],
			containerQuantity: [
				{
					text: "1台",
					value: 1,
				},
				{
					text: "2台",
					value: 2,
				},
				{
					text: "3台",
					value: 3,
				},
				{
					text: "4台",
					value: 4,
				},
				{
					text: "5台",
					value: 5,
				},
				{
					text: "6台",
					value: 6,
				},
				{
					text: "7台",
					value: 7,
				},
				{
					text: "8台",
					value: 8,
				},
				{
					text: "9台",
					value: 9,
				},
				{
					text: "10台",
					value: 10,
				},
			],
			id: undefined,
		};
	},
	computed: {
		submitDisabled() {
			return !(
				(
					this.formState.deviceSn &&
					// this.device &&
					this.formState.address &&
					this.formState.stationName &&
					this.formState.areaId &&
					this.formState.electricType
				)
				// && this.formState.voltage
			);
		},
	},
	onReady() {
		const pages = getCurrentPages();
		const currentPage = pages[pages.length - 1];
		const { options, route } = currentPage;
		if (options && options.device) {
			this.device = options.device;
		}
		this.getAreaList();
		this.login();
	},

	onShow() {
		//#ifdef MP-DINGTALK
		this.id = dd.corpId;
		//#endif
		const map = uni.getStorageSync("map");
		if (map) {
			const deatil = JSON.parse(map);
			if (deatil.pname == deatil.cityname) {
				this.formState.address = deatil.cityname + deatil.adname + deatil.address;
			} else {
				this.formState.address = deatil.pname + deatil.cityname + deatil.adname + deatil.address;
			}
			this.formState.province = deatil.pname;
			this.formState.city = deatil.cityname;
			this.formState.district = deatil.adname;
			this.formState.longitude = deatil.location[0];
			this.formState.latitude = deatil.location[1];
			// map 存放经纬度和位置信息
			uni.removeStorageSync("map"); // 读取完之后删除
		}
		const that = this;
		disposeAuthData(async (options) => {
			if (options.status == "ok") {
				let { authCode } = options.result;
				if (authCode) {
					uni.showLoading({
						title: "正在登录",
					});
					let result = await service.getAuthorizationPhone({
						authCode,
						dingTalkCropId: this.id,
						deviceType: "DING_TALK_MINI_APP",
					});
					const {
						data: { code, data },
					} = result;
					if (code === 0) {
						uni.setStorageSync("accessToken", data.token);
						const userInfo = await store.dispatch("user/getCustomerInfo");
						const { realName, orgName, userId } = userInfo.data.data;
						that.orgName = orgName;
						that.userId = userId;
						that.userName = realName;
					}
					// that.phone = result.data.data;
					uni.hideLoading();
					// if (that.clickedSubmit) {
					//   that.submit();
					// }
				}
			}
		});
	},
	setup() {
		const formState = reactive({
			deviceSn: void 0,
			address: undefined,
			stationName: undefined,
			areaId: undefined,
			electricType: 1,
			// voltage: "10kV",
			containerQuantity: 1,

			//新增字段
			province: void 0,
			city: void 0,
			district: void 0,
			address: void 0,
			latitude: void 0,
			longitude: void 0,
		});
		const containerQuantityNum = ref("215KWh");

		return {
			formState,
			containerQuantityNum,
		};
	},
	onLoad(option) {
		if (option?.device) {
			const { device } = option;
			const startIndex = device.indexOf("【") != -1 ? device.indexOf("【") : device.indexOf("[");
			const endIndex = device.indexOf("】") != -1 ? device.indexOf("】") : device.indexOf("]");
			if (startIndex != -1 && endIndex != -1) {
				this.formState.deviceSn = device.slice(startIndex + 1, endIndex);
			}
		}
	},
	methods: {
		login() {
			const that = this;
			uni.showLoading({
				title: "正在加载",
				mask: true,
			});

			//#ifdef MP-DINGTALK
			dd.getAuthCode({
				success: async function (res) {
					let param = {
						dingTalkCropId: this.id,
						accessCode: res.authCode,
						deviceType: "DING_TALK_MINI_APP",
					};
					let result = await service.DDFreeLogin(param);
					const {
						data: { code, data },
					} = result;
					uni.hideLoading();
					if (code === 0 && data.bizCode === 10012) {
						that.showAuth();
					}
					if (code === 0 && data.bizCode !== 10012) {
						uni.setStorageSync("accessToken", data.token);
						const userInfo = await store.dispatch("user/getCustomerInfo");
						const { realName, orgName, userId } = userInfo.data.data;
						that.orgName = orgName;
						that.userId = userId;
						that.userName = realName;
					}
				},
				fail: function (err) {
					uni.hideLoading();
				},
			});
			// }
			// #endif
		},
		showAuth() {
			// if (this.agree) {
			return openAuthMiniApp({
				path: "pages/home/<USER>", //不要改,这里是小程序dingwlanwvdmrtjjwdmd下的一个页面地址
				panelHeight: "percent50",
				extraData: {
					clientId: dingTalkClientId, // 应用ID(唯一标识)
					rpcScope: "Contact.User.Read",
					fieldScope: "Contact.User.mobile",
					type: 0,
					ext: JSON.stringify({}),
					from: "",
				},
			});
			// } else {
			//   uni.showToast({
			//     title: "请先勾选下方协议",
			//   });
			// }
		},
		cancelAuth() {
			return openAuthMiniApp({
				path: "pages/cancel/index",
				extraData: {
					clientId: dingTalkClientId,
					ext: "{}",
					from: "", // 'aliCloud'
				},
			});
		},
		async submit() {
			// if (!this.phone) {
			//   this.showAuth();
			//   this.clickedSubmit = true;
			// } else {
			uni.showLoading({
				title: "正在绑定...",
			});
			let param = {
				// cropId: this.id,
				// userId: this.userId,
				deviceSn: this.formState.deviceSn,
				stationName: this.formState.stationName,
				address: this.formState.address,
				// 新参数
				areaId: this.formState.areaId,
				electricType: this.formState.electricType,
				// voltage: this.formState.voltage,

				province: this.formState.province,
				city: this.formState.city,
				district: this.formState.district,
				address: this.formState.address,
				latitude: this.formState.latitude,
				longitude: this.formState.longitude,
				containerQuantity: this.formState.containerQuantity,
			};
			const {
				data: { code },
			} = await service.bindDevice(param);
			uni.hideLoading();
			if (code === 0) {
				uni.showToast({
					title: "绑定成功",
					icon: "none",
					position: "center",
				});
				uni.reLaunch({
					url: "/pages/device/index",
				});
			}
		},
		async getAreaList() {
			let result = await service.queryAreaList();
			this.formState.areaId = result.data.data[0].id;
			this.areas = result.data.data.map((item) => {
				return {
					text: item.areaName,
					value: item.id,
				};
			});
		},
		inputChange(key) {
			setTimeout(() => {
				this.formState[key] = this.formState[key].replace(/[^\w\x21-\x2f\x3a-\x40\x5b-\x60\x7B-\x7F]/g, "");
			}, 10);
		},
		quantityChange(e) {
			let num = e * 215;
			this.containerQuantityNum = num >= 1000 ? this.roundNumFun(num / 1000, 2) + "MWh" : num + "KWh";
		},
		roundNumFun(num, decimals) {
			const fraction = Math.pow(10, decimals);
			const integerPart = Math.round(num * fraction);
			let roundNum = integerPart / fraction;
			if (roundNum) {
				const num = roundNum.toString();
				if (num.indexOf(".") != -1) {
					const nums = num.slice(num.indexOf(".") + 1);
					if (nums.length == 1) {
						return roundNum + "0";
					}
					return roundNum;
				}
			}
			return roundNum;
		},
		iconClick() {
			uni.navigateTo({
				url: "/packageDevice/map",
			});
		},
	},
};
</script>
<style lang="scss">
.bind {
	.uni-forms-item {
		// margin-bottom: 32rpx;
		// padding-bottom: 16rpx;
		// line-height: 44rpx !important;
		display: flex;
		align-items: center;
	}
	// .uni-easyinput__content-input {
	//   padding: 0 !important;
	//   height: 22px;
	//   line-height: 22px;
	// }
	// .uni-forms-item__content,
	// .uni-easyinput__content-input {
	//   font-size: 28rpx;
	// }
}
</style>
<style lang="scss" scoped>
.from-label-title {
	font-size: 28rpx;
	color: #222222;
	line-height: 40rpx;
}
.from-item {
	margin: 20rpx 0;
}
.form-label {
	width: 144rpx;
	// width: 204rpx;
	height: 92rpx;
	line-height: 92rpx;
	text.iconfont {
		font-size: 44rpx;
		margin-right: 16rpx;
		color: #99a8b1;
	}
	text.iconfonts {
		font-size: 44rpx;
		margin-right: 16rpx;
		color: #99a8b1;
	}
	text {
		vertical-align: middle;
		font-size: 28rpx;
	}
}
.primary {
	width: 480rpx;
	// left: 136rpx;
	// right: 136rpx;
	// position: absolute;
	// bottom: 96rpx;
	margin: 0 auto;
	margin-top: 254rpx;
}
.form-list {
	line-height: 92rpx;
	height: 92rpx;
	border-bottom: 1px solid rgba(34, 34, 34, 0.08);
}
:deep(.uni-forms-item) {
	margin-bottom: 0 !important;
}
:deep(.uni-forms-item__label) {
	padding-right: 0;
	height: 92rpx;
	line-height: 92rpx;
	color: $uni-secondary-color;
}
:deep(.uni-select__input-box) {
	height: 92rpx;
	border: none;
	text-align: right;
	column-gap: 16rpx;
}
:deep(.uni-easyinput__content) {
	background: transparent !important;
}
:deep(.uni-easyinput__content-input) {
	height: 92rpx;
	border: none;
	text-align: right;
	column-gap: 16rpx;
	background: transparent;
}
:deep(.uni-select) {
	border: none;
	height: 92rpx;
	line-height: 92rpx;
}
:deep(.uni-select__input-placeholder) {
	color: #cfcfcf;
	font-size: 28rpx;
}
:deep(.uni-select--disabled) {
	background: transparent !important;
}
:deep(.uni-select__selector-item) {
	padding: 10px 10px;
}
</style>
