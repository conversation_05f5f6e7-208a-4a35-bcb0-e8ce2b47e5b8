<template>
  <view class="bind">
    {{ msg }}
  </view>
</template>
<script>
import { reactive, toRefs } from "vue";
import service from "@/apiService/device";
import serveApi from "@/apiService";
import { dingTalkClientId } from "@/common/env";
import store from "@/store/index.js";
//#ifdef MP-DINGTALK
import {
  openAuthMiniApp,
  disposeAuthData,
} from "dingtalk-design-libs/biz/openAuthMiniApp";
// import dd from "dingtalk-jsapi";
//#endif
import { onShow } from "@dcloudio/uni-app";

export default {
  data() {
    return {};
  },
  computed: {},
  onReady() {
    this.binding();
  },

  setup() {
    const state = reactive({
      bindUserId: undefined,
      msg: "",
    });
    onShow(async () => {
      const pages = getCurrentPages();
      const options = pages[pages.length - 1].options;
      // state.bindUserId = options.scene.split("%3D")[1];
      state.bindUserId = options.userId
        ? options.userId
        : options.scene.split("%3D")[1];
      // state.bindUserId = "1777166359648583682";
    });
    return {
      ...toRefs(state),
    };
  },
  onLoad(option) {},
  methods: {
    async binding() {
      const that = this;
      //#ifdef MP-DINGTALK
      uni.showLoading({
        title: "正在绑定第三方账户",
        mask: true,
      });
      dd.getAuthCode({
        success: async function (res) {
          try {
            const {
              data: { data, code },
            } = await serveApi.thirdUserLogin({
              dingTalkCropId: dd.corpId,
              code: res.authCode,
              deviceType: "DING_TALK_MINI_APP",
            });

            uni.hideLoading();
            if (code === 0) {
              if (data.token) {
                uni.setStorageSync("accessToken", data.token);
                uni.setStorageSync("corpId", dd.corpId);
                setTimeout(() => {
                  uni.switchTab({
                    url: "/pages/device/index",
                  });
                }, 500);
              } else {
                let params = {
                  // platform: "DING_TALK",
                  dingTalkCropId: dd.corpId,
                  // accessCode: res.authCode,
                  bindUserId: that.bindUserId,
                  thirdUserId: data.thirdUserId,
                  deviceType: "DING_TALK_MINI_APP",
                };
                try {
                  // return false;
                  let result = await service.bindThirdAccount(params);
                  if (
                    result.data.code === 0 &&
                    result.data.data.bizCode === 0
                  ) {
                    // 绑定成功
                    uni.setStorageSync("accessToken", result.data.data.token);
                    uni.setStorageSync("corpId", dd.corpId);
                    setTimeout(() => {
                      uni.switchTab({
                        url: "/pages/device/index",
                      });
                    }, 500);
                    // uni.reLaunch({
                    // 	url: "/packageDevice/login",
                    // });
                  }
                } catch (error) {}
              }
              uni.hideLoading();
            }
          } catch (error) {
            uni.hideLoading();
          }
        },
        fail: function (err) {
          uni.hideLoading();
        },
      });
      // }
      //#endif
      //#ifdef MP-WEIXIN
      wx.showLoading({
        title: "正在绑定第三方账户",
        mask: true,
      });
      wx.login({
        success: async (res) => {
          try {
            uni.removeStorageSync("accessToken");
            store.commit("user/setCustomerInfo", {});
            store.commit("device/setOrgId", "");
            store.commit("device/setData", []);
            const {
              data: { data, code },
            } = await serveApi.thirdUserLogin({
              code: res.code,
              deviceType: "WECHAT_MINI_APP",
            });
            // uni.setStorageSync("thirdUserId", data.thirdUserId);
            wx.hideLoading();
            if (code === 0) {
              if (data.token) {
                uni.setStorageSync("accessToken", data.token);
                setTimeout(() => {
                  uni.switchTab({
                    url: "/pages/device/index",
                  });
                }, 500);
                wx.hideLoading();
              } else {
                let params = {
                  bindUserId: that.bindUserId,
                  thirdUserId: data.thirdUserId,
                  deviceType: "WECHAT_MINI_APP",
                };
                try {
                  // return false;
                  let result = await service.bindThirdAccount(params);
                  if (
                    result.data.code === 0 &&
                    result.data.data.bizCode === 0
                  ) {
                    // 绑定成功
                    uni.setStorageSync("accessToken", result.data.data.token);
                    setTimeout(() => {
                      uni.switchTab({
                        url: "/pages/device/index",
                      });
                    }, 500);
                    // uni.reLaunch({
                    // 	url: "/packageDevice/login",
                    // });
                  }
                } catch (error) {
                  wx.hideLoading();
                }
              }
              wx.hideLoading();
            } else {
            }
          } catch (error) {
            wx.hideLoading();
          }
        },
      });
      //#endif
    },
  },
};
</script>
<style lang="scss"></style>
