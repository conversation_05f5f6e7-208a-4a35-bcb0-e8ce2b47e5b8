<template>
  <view class="bind">
    <!-- <button type="primary" @click="scanClick">测试点击扫二维码</button> -->
    <empty isPage tips="当前账号尚未绑定，请登录储能后台进行绑定" />
  </view>
</template>
<script>
import Empty from "@/components/empty.vue";
import { onMounted } from "vue";
export default {
  components: { Empty },
  setup() {
    onMounted(() => {
      uni.showLoading();
      uni.hideLoading();
    });
    const scanClick = () => {
      uni.scanCode({
        success(res) {
          // debugger;
          //#ifdef MP-DINGTALK
          const userIdMatch = res.result.match(/userId%3D([^&]*)/);
          if (userIdMatch) {
            const userId = userIdMatch[1]; // 获取 userId 参数的值
            // 跳转到绑定页面并传递 userId
            uni.navigateTo({ url: `/pages/bind/binding?userId=${userId}` });
          } else {
          }
          //#endif
          //#ifdef MP-WEIXIN
          const userIdPath = res.path.match(/userId%3D([^&]*)/);
          if (userIdPath) {
            const userId = userIdPath[1]; // 获取 userId 参数的值
            // 跳转到绑定页面并传递 userId
            uni.navigateTo({ url: `/pages/bind/binding?userId=${userId}` });
          } else {
          }
          //#endif
        },
      });
    };
    return {
      scanClick,
    };
    // onMounted(){
    //    uni.hideLoading();
    // }
  },
};
</script>
<style scoped>
button {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 320rpx;
  transform: translate(-50%, -50%);
}
</style>
