<template>
	<div class="info">
		<div class="list">基础信息</div>
		<div class="list list-flex">
			<div class="text-secondar-text w-18">{{ title }}名称</div>
			<div class="flex-1">{{ detailInfo?.name }}</div>
		</div>
		<div class="list list-flex">
			<div class="text-secondar-text w-18">{{ title }}编号</div>
			<div class="flex-1">{{ detailInfo?.orderNo }}</div>
		</div>

		<div class="list list-flex">
			<div class="text-secondar-text w-18">{{ title }}状态</div>
			<div class="flex-1">
				<dictionary :statusOptions="orderStatus" :value="detailInfo?.orderStatus" />
			</div>
		</div>
		<div class="list list-flex">
			<div class="text-secondar-text w-18">{{ title }}类型</div>
			<div class="flex-1">
				{{ getOrderType(detailInfo?.orderType) }}
			</div>
		</div>
		<div class="list list-flex">
			<div class="text-secondar-text w-18">跟进人</div>
			<div class="flex-1">
				{{ detailInfo?.disposeStaffName || "-" }}
			</div>
		</div>
		<div class="list list-flex">
			<div class="text-secondar-text w-18">处理时长</div>
			<div class="flex-1">{{ detailInfo?.processDuration }}小时</div>
		</div>
		<div class="list list-flex">
			<div class="text-secondar-text w-18">创建时间</div>
			<div class="flex-1">{{ detailInfo?.createTime }}</div>
		</div>
		<div class="list" v-if="detailInfo?.orderType != 4">
			<div class="list">关联告警信息</div>
			<div class="p-3 bg-background rounded-lg relation">
				<div class="list list-flex">
					<div class="text-secondar-text w-18">告警名称</div>
					<div class="flex-1">
						{{ detailInfo?.relationAlarm?.alarmDesc }}
					</div>
				</div>
				<div class="list list-flex">
					<div class="text-secondar-text w-18">告警编号</div>
					<div class="flex-1">
						{{ detailInfo?.relationAlarm?.deviceSn }}
					</div>
				</div>
				<div class="list list-flex">
					<div class="text-secondar-text w-18">告警状态</div>
					<div class="flex-1">
						<dictionary :statusOptions="alarmStatusList" :value="detailInfo?.relationAlarm?.alarmStatus" />
					</div>
				</div>
				<div class="list list-flex">
					<div class="text-secondar-text w-18">告警级别</div>
					<div class="flex-1">
						<dictionary :statusOptions="alarmLevelList" :value="detailInfo?.relationAlarm?.alarmLevel" isBackgroundColor />
					</div>
				</div>
				<div class="list list-flex">
					<div class="text-secondar-text w-18">站点名称</div>
					<div class="flex-1">
						{{ detailInfo?.relationAlarm?.stationName }}
					</div>
				</div>
				<div class="list list-flex">
					<div class="text-secondar-text w-18">站点编号</div>
					<div class="flex-1">
						{{ detailInfo?.relationAlarm?.stationNo }}
					</div>
				</div>
				<div class="list list-flex">
					<div class="text-secondar-text w-18">机柜编号</div>
					<div class="flex-1">
						{{ detailInfo?.relationAlarm?.containerNo }}
					</div>
				</div>
				<div class="list list-flex">
					<div class="text-secondar-text w-18">设备类型</div>
					<div class="flex-1">
						{{ detailInfo?.relationAlarm?.deviceType }}
					</div>
				</div>
				<div class="list list-flex">
					<div class="text-secondar-text w-18">设备编号</div>
					<div class="flex-1">
						{{ detailInfo?.relationAlarm?.deviceSn }}
					</div>
				</div>
				<div class="list list-flex">
					<div class="text-secondar-text w-18">告警时间</div>
					<div class="flex-1">
						{{ detailInfo?.relationAlarm?.alarmTime }}
					</div>
				</div>
			</div>
		</div>
		<div v-else class="list">
			<div class="p-3 bg-background rounded-lg relation">
				<div class="list list-flex text-secondar-text">补充信息 {{ detailInfo?.problemDescription }}</div>
			</div>
		</div>
		<div class="list" v-show="isOperator">
			<div class="list">工单处理信息</div>
			<div class="">
				<uni-forms :modelValue="formState" ref="valiForm" :rules="rules" label-position="top">
					<div class="relation">
						<uni-forms-item label="发生原因" :name="'cause'">
							<uni-easyinput
								type="textarea"
								v-model="formState.cause"
								:disabled="!isOperator || detailInfo?.orderStatus == 2"
								placeholder="请输入发生原因的补充说明"
								:style="{ backgroundColor: 'transparent', border: 'none' }"
								placeholderStyle="font-size: 28rpx;color: rgba(34, 34, 34, 0.2);"
								primaryColor="#6FBECE"
								maxlength="200">
							</uni-easyinput>
						</uni-forms-item>
					</div>
					<div class="relation">
						<uni-forms-item label="维修记录" :name="'solution'">
							<uni-easyinput
								type="textarea"
								v-model="formState.solution"
								:disabled="!isOperator || detailInfo?.orderStatus == 2"
								placeholder="请输入维修记录的补充说明"
								:style="{ backgroundColor: 'transparent', border: 'none' }"
								placeholderStyle="font-size: 28rpx;color: rgba(34, 34, 34, 0.2);"
								primaryColor="#6FBECE"
								maxlength="200">
							</uni-easyinput>
						</uni-forms-item>
					</div>
				</uni-forms>
				<view class="upload" v-if="detailInfo?.orderStatus != 2 || (detailInfo?.orderStatus == 2 && imageFileList.length)">
					<uni-file-picker
						:sizeType="['compressed']"
						limit="9"
						:title="detailInfo?.orderStatus == 2 ? '图片说明：' : '上传图片（最多9张）'"
						@select="selectFile"
						v-model="imageFileList"
						:readonly="!isOperator || detailInfo?.orderStatus == 2"
						:disabled="!isOperator || detailInfo?.orderStatus == 2"></uni-file-picker>
				</view>

				<!-- <empty v-if="detailInfo?.orderStatus == 2 && imageFileList.length == 0" value="未上传图片" /> -->
				<!-- 按钮需要添加权限验证 -->
				<view @click="onSubmit" class="add-btn" v-if="isOperator && detailInfo?.orderStatus != 2">解决工单</view>
			</div>
		</div>
	</div>
</template>

<script setup>
import { ref, reactive, computed } from "vue";
import { onShow, onPullDownRefresh, onLoad } from "@dcloudio/uni-app";
import service from "../apiService/device";
import { orderTypes, orderStatus, alarmStatusList, alarmLevelList } from "@/common/constant";
import store from "@/store/index.js";
import Dictionary from "@/components/dictionary.vue";
// import empty from "@/components/empty.vue";
// const pageInfo = ref();
// 获取页面传递的数据
const orderId = ref();
onLoad(async () => {
	const pages = getCurrentPages();
	const options = pages[pages.length - 1].options;
	// pageInfo.value = options;
	uni.showLoading();
	await getSpecialWorkDetail(options.id);
	orderId.value = options.id;
	uni.hideLoading();
	uni.setNavigationBarTitle({
		title: isOperator.value ? "工单详情" : "风险详情",
	});
});
const detailInfo = ref();
const formState = reactive({
	cause: "",
	solution: "",
});
const imgList = ref([]);
const hideUpload = ref(false);
const getSpecialWorkDetail = async (id) => {
	let res = await service.getWorkOrderDetail(id);
	detailInfo.value = res.data.data;
	formState.cause = res.data.data.cause || "";
	formState.solution = res.data.data.solution || "";
	imageFileList.value =
		(res.data.data.files &&
			res.data.data.files.map((item) => {
				return {
					name: "图片",
					url: item.fileVisitUrl,
				};
			})) ||
		[];
	hideUpload.value = res.data.data.orderStatus == 2;
};
const getOrderType = (val) => {
	return orderTypes.find((item) => item.value === val)?.label;
};

const imageFileList = ref([]);
const selectFile = (e) => {
	console.log("[ e ] >", e);
	imageFileList.value = [].concat(...imageFileList.value, ...e.tempFiles);
};
const valiForm = ref(null);
const rules = {
	cause: {
		rules: [
			{
				required: true,
				errorMessage: "发生原因不能为空",
			},
		],
	},
	solution: {
		rules: [
			{
				required: true,
				errorMessage: "解决方案不能为空",
			},
			// {
			// 	format: "number",
			// 	errorMessage: "年龄只能输入数字",
			// },
		],
	},
};
const onSubmit = async () => {
	const fileIds = imageFileList.value.map((item) => item.fileId);
	console.log(fileIds);
	valiForm.value
		.validate()
		.then(async () => {
			uni.showLoading();
			// 上传图片
			const tempFilePaths = imageFileList.value.map((item) => item.path);
			const url = "https://ems-api.ssnj.com";
			const token = uni.getStorageSync("accessToken");
			let files = [];
			const uploadPromises = tempFilePaths.map((item) => {
				return new Promise((resolve, reject) => {
					uni.uploadFile({
						url: url + "/file/uploadFile",
						filePath: item,
						name: "file",
						fileType: "image",
						header: {
							"Content-Type": "multipart/form-data",
							Authorization: `Bearer ${token}`,
						},
						formData: {
							scene: "inspectionPic",
						},
						success: (uploadFileRes) => {
							console.log("[ uploadFileRes ] >", uploadFileRes);
							const res = JSON.parse(uploadFileRes.data);
							console.log("[ res ] >", res);
							if (res.code === 0) {
								const fileData = {
									name: "xxx.png",
									extname: "png",
									url: res.data.fileVisitUrl,
									fileId: res.data.fileId,
								};
								files.push(fileData);
								resolve(fileData);
							} else {
								reject("Upload failed");
							}
						},
						fail: (error) => {
							console.log("[ error ] >", error);
							reject(error);
						},
					});
				});
			});
			Promise.all(uploadPromises)
				.then(() => {
					const fileIds = files.map((item) => item.fileId);
					let params = {
						...formState,
						id: orderId.value,
						fileIds: fileIds,
						orderStatus: 2,
					};
					console.log("[ params ] >", params);
					service.updateWorkOrder(params).then((res) => {
						if (res.data.data) {
							uni.showToast({
								title: "解决成功",
								icon: "success",
								success: () => {
									uni.navigateBack();
								},
							});
						}
					});
				})
				.catch((error) => {
					console.log("[ Upload Error ] >", error);
					uni.hideLoading();
				});
			uni.hideLoading();
		})
		.catch((err) => {
			console.log(err);
			uni.showToast({
				title: err[0].errorMessage,
				icon: "none",
			});
			uni.hideLoading();
		});
};
const isOperator = computed(() => {
	return store.state.user.customerInfo.roles.includes("operation_staff");
	// return true;
});
</script>

<style lang="scss" scoped>
.text-secondar-text {
	color: $uni-secondary-color;
}
.w-18 {
	width: 144rpx;
}
.list {
	line-height: 44rpx;
	& + .list {
		margin-top: 24rpx;
	}
}
.list-flex {
	display: flex;
	justify-content: flex-start;
}
.info {
	padding: 24rpx;
	padding-top: 48rpx;
	background: #ffffff;
	border-top: 24rpx solid #f5f7f7;
}
.relation {
	padding: 24rpx;
	border-radius: 16rpx;
	background: #f5f7f7;
	margin-top: 24rpx;
	padding-bottom: 12rpx;
	:deep(.uni-easyinput__content-textarea) {
		padding-bottom: 36rpx;
	}
}
:deep(.uni-easyinput__content) {
	border: none !important;
	background-color: transparent !important;
	margin: 0 !important;
}
:deep(.uni-easyinput__content-textarea) {
	background: none !important;
	padding: 0;
	height: 120rpx;
	min-height: 120rpx;
	height: 60px;
	color: $uni-base-color !important;
}
:deep(.uni-forms-item) {
	margin-bottom: 0 !important;
}
:deep(.uni-forms-item__label) {
	height: 32px !important;
	color: $uni-secondary-color !important;
}
:deep(.uni-forms-item.is-direction-top .uni-forms-item__label) {
	padding: 0;
}

:deep(.icon-del-box) {
	top: 5px;
	bottom: initial;
}
.add-btn {
	width: 480rpx;
	height: 92rpx;
	line-height: 92rpx;
	margin: 0 auto;
	margin-top: 48rpx;
	background: #f6f7f7;
	border-radius: 16rpx;
	text-align: center;
	font-size: 28rpx;
	color: #6fbece;
}
.empty-text {
	font-size: 24rpx;
	color: $uni-secondary-color;
	text-align: center;
	line-height: 80rpx;
}
// :deep(.uni-easyinput .uni-easyinput__count) {
// 	color: #ff0000;
// }
// ::v-deep .uni-easyinput .uni-easyinput__count {
// 	color: #ff0000;
// }
.upload {
	margin-top: 16rpx;
}
:deep(.file-title) {
	color: $uni-secondary-color !important;
}
:deep(.file-count) {
	display: none !important;
}
</style>
