<template>
	<div class="add">
		<view class="forms">
			<uni-forms :modelValue="formState" :label-width="80" border>
				<view>
					<uni-forms-item label="工单名称" :name="'name'">
						<uni-easyinput
							v-model="formState.name"
							placeholder="请输入工单名称"
							:inputBorder="false"
							placeholder-class="search-input-placeholder"
							placeholderStyle="font-size: 28rpx;color: rgba(34, 34, 34, 0.2);text-align: right;"
							primaryColor="#6FBECE"
							maxlength="24"></uni-easyinput>
					</uni-forms-item>
					<uni-forms-item label="选择站点" :name="'stationId'">
						<uni-data-select v-model="formState.stationId" :localdata="orgs" @change="changeStation" placeholder="请选择站点"></uni-data-select>
					</uni-forms-item>
					<uni-forms-item label="跟进人" :name="'disposeStaffId'">
						<uni-data-select v-model="formState.disposeStaffId" :localdata="disposeStaffs" @change="changeStaff" placeholder="请选择跟进人"></uni-data-select>
					</uni-forms-item>
					<view class="description">
						<view class="text-secondary title">补充说明：</view>
						<uni-easyinput
							type="textarea"
							v-model="formState.problemDescription"
							placeholder="请对工单的详细内容进行补充说明"
							:style="{ backgroundColor: 'transparent', border: 'none', marginTop: '8rpx' }"
							placeholderStyle="font-size: 28rpx;color: rgba(34, 34, 34, 0.2);"
							primaryColor="#6FBECE"
							maxlength="200">
						</uni-easyinput>
					</view>
				</view>
			</uni-forms>
		</view>
		<view @click="onSubmit" class="add-btn">创建工单</view>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { onShow } from "@dcloudio/uni-app";
import service from "../apiService/device";
import store from "@/store/index.js";
// const stationInfo = ref();
// 获取页面传递的数据
onShow(async () => {
	const pages = getCurrentPages();
	const options = pages[pages.length - 1].options;
});

const formState = reactive({
	name: undefined,
	stationId: undefined,
	disposeStaffId: undefined,
	problemDescription: undefined,
});
const orgs = ref([]);
const getOrgAndSubOrgStationNameList = async () => {
	const res = await service.getOrgAndSubOrgStationNameList(store.state.user.customerInfo.orgId || "1726907974555729921");
	orgs.value = res.data.data.map((item) => {
		return {
			text: item.stationName,
			value: item.id,
		};
	});
};
const changeStation = () => {
	//
};

const disposeStaffs = ref([]);
const getStaffByRole = async () => {
	const res = await service.getStaffByRole({
		roleId: 3,
	});
	disposeStaffs.value = res.data.data.map((item) => {
		return {
			text: item.name,
			value: item.id,
		};
	});
};

const changeStaff = () => {};
onMounted(async () => {
	await getOrgAndSubOrgStationNameList();
	await getStaffByRole();
});

const onSubmit = async () => {
	if (formState.name && formState.stationId && formState.disposeStaffId) {
		const params = {
			...formState,
		};
		console.log("[ params ] >", params);
		let res = await service.manualCreate(params);
		if (res.data.data) {
			uni.showToast({
				title: "添加成功",
				icon: "success",
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 200);
		}
	} else {
		uni.showToast({
			title: !formState.name ? "请填写工单名称！" : !formState.stationId ? "请选择站点！" : !formState.disposeStaffId ? "请选择跟进人！" : "",
			icon: "error",
		});
	}
};
</script>

<style lang="scss" scoped>
.add {
	padding: 24rpx;
	padding-top: 0;
	background: #ffffff;
	border-top: 24rpx solid #f5f7f7;
}
.info {
	padding: 24rpx;
	border-radius: 16rpx;
	background: #f5f7f7;
}
:deep(.uni-forms-item) {
	align-items: center;
	margin-bottom: 0 !important;
}
:deep(.uni-forms-item__label) {
	height: 108rpx !important;
}

.sm {
	font-size: 24rpx;
}
.text-secondary {
	color: $uni-secondary-color;
}
.add-btn {
	width: 480rpx;
	height: 92rpx;
	line-height: 92rpx;
	margin: 0 auto;
	margin-top: 48rpx;
	background: #f6f7f7;
	border-radius: 16rpx;
	text-align: center;
	font-size: 28rpx;
	color: #6fbece;
	position: fixed;
	left: 50%;
	margin-left: -240rpx;
	bottom: 120rpx;
}
:deep(.uni-select__selector-item) {
	font-size: 28rpx;
	line-height: 64rpx;
	height: 64rpx;
	vertical-align: middle;
	align-items: center;
	color: $uni-base-color;
}
:deep(.uni-select) {
	border: none;
	padding: 0 !important;
	text-align: right;
}

:deep(.uni-forms-item--border) {
	padding: 0;
}
:deep(.uni-easyinput__content-input) {
	padding: 0 !important;
	text-indent: 0 !important;
	text-align: right;
}
:deep(.uni-select__input-placeholder) {
	font-size: 28rpx;
	color: rgba(34, 34, 34, 0.2);
	text-align: right;
}
.description {
	padding: 24rpx;
	border-radius: 16rpx;
	background: #f5f7f7;
	margin-top: 16rpx;
	padding-bottom: 12rpx;
	width: 100%;
	:deep(.uni-easyinput__content) {
		border: none !important;
		background-color: transparent !important;
	}
	:deep(.uni-easyinput__content-textarea) {
		background: none !important;
		padding: 0;
		padding-bottom: 36rpx;
		height: 120rpx;
		min-height: 120rpx;
		color: $uni-base-color !important;
	}
	.title {
		height: 32px;
		line-height: 32px;
		text-align: left;
		white-space: initial;
	}
}
</style>
