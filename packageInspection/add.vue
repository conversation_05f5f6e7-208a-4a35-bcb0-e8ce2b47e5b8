<template>
	<div class="add">
		<view class="info">{{ stationInfo?.stationName }}-{{ stationInfo?.containerNo.substring(3) + "机柜" }}</view>
		<view class="forms">
			<uni-forms :modelValue="formState" :label-width="120">
				<view v-for="(item, index) in formState.inspectionItems" :key="index">
					<uni-forms-item :label="item.label" :name="item.item">
						<uni-data-checkbox
							:multiple="false"
							v-model="item.status"
							:localdata="[
								{ text: '正常', value: 1 },
								{ text: '异常', value: 2 },
							]"
							@change="changeStatus($event, item)" />
					</uni-forms-item>
					<view v-if="item.status == 2">
						<uni-easyinput
							v-model="item.description"
							placeholder="可以对异常情况进行说明"
							:style="{ border: '1px solid #ff3141', backgroundColor: '#fff' }"
							primaryColor="#6FBECE"
							maxlength="100"></uni-easyinput>
					</view>
				</view>
			</uni-forms>
		</view>
		<view class="description">
			<view class="text-secondary">补充说明：</view>
			<uni-easyinput
				type="textarea"
				v-model="formState.description"
				placeholder="请输入补充说明，如果没有，可不填"
				:style="{ backgroundColor: 'transparent', border: 'none' }"
				primaryColor="#6FBECE"
				maxlength="200"></uni-easyinput>
		</view>
		<view class="upload">
			<uni-file-picker :sizeType="['compressed']" limit="9" title="上传图片（最多9张）" @select="selectFile" v-model="imageFileList" @delete="onDelete"></uni-file-picker>
		</view>

		<view @click="onSubmit" class="add-btn">添加记录</view>
	</div>
</template>

<script setup>
import { ref, reactive, nextTick } from "vue";
import { onShow, onUnload } from "@dcloudio/uni-app";
import service from "../apiService/device";
const stationInfo = ref();
// 获取页面传递的数据
onShow(async () => {
	const pages = getCurrentPages();
	const options = pages[pages.length - 1].options;
	stationInfo.value = options;
});

const defaultInspectionItems = [
	{
		item: "overallSummary",
		label: "总体概况",
		status: 1,
		description: undefined,
	},
	{
		item: "batteryCompartment",
		label: "电池舱",
		status: 1,
		description: undefined,
	},
	{
		item: "highVoltageCabinet",
		label: "高压箱",
		status: 1,
		description: undefined,
	},
	{
		item: "combiner",
		label: "汇流柜",
		status: 1,
		description: undefined,
	},
	{
		item: "ems",
		label: "能量管理系统",
		status: 1,
		description: undefined,
	},

	{
		item: "pcs",
		label: "逆变器",
		status: 1,
		description: undefined,
	},
	{
		item: "bms",
		label: "电池系统",
		status: 1,
		description: undefined,
	},
	{
		item: "fireProtection",
		label: "消防系统",
		status: 1,
		description: undefined,
	},
	{
		item: "refrigerator",
		label: "液冷机",
		status: 1,
		description: undefined,
	},
];
const formState = reactive({
	inspectionItems: defaultInspectionItems,
	description: undefined,
});

const changeStatus = (event, item) => {};
const imageFileList = ref([]);
const selectFile = (e) => {
	imageFileList.value = [].concat(...imageFileList.value, ...e.tempFiles);
};
const onDelete = (e) => {
	console.log("[ e ] >", e);
};
onUnload((options) => {
	console.log("[ onunload ] >", options);
});
const onSubmit = async () => {
	console.log("[ onSubmit ] >");
	uni.showLoading();
	// 上传图片
	const tempFilePaths = imageFileList.value.map((item) => item.path);
	const url = "https://ems-api.ssnj.com";
	const token = uni.getStorageSync("accessToken");
	let files = [];
	const uploadPromises = tempFilePaths.map((item) => {
		return new Promise((resolve, reject) => {
			uni.uploadFile({
				url: url + "/file/uploadFile",
				filePath: item,
				name: "file",
				fileType: "image",
				header: {
					"Content-Type": "multipart/form-data",
					Authorization: `Bearer ${token}`,
				},
				formData: {
					scene: "inspectionPic",
				},
				success: (uploadFileRes) => {
					const res = JSON.parse(uploadFileRes.data);
					if (res.code === 0) {
						const fileData = {
							name: "xxx.png",
							extname: "png",
							url: res.data.fileVisitUrl,
							fileId: res.data.fileId,
						};
						files.push(fileData);
						resolve(fileData);
					} else {
						reject("Upload failed");
					}
				},
				fail: (error) => {
					reject(error);
				},
			});
		});
	});
	// 上传所有图片后
	Promise.all(uploadPromises)
		.then(() => {
			const inspectionItems = formState.inspectionItems.map((i) => {
				return {
					item: i.item,
					status: i.status,
					description: i.description,
				};
			});
			const fileIds = files.map((item) => item.fileId);
			let params = {
				inspectionItems,
				fileIds: fileIds,
				deviceId: stationInfo.value.deviceId,
				description: formState.description,
			};
			uni.setStorageSync("refreshInspect", true);
			// setTimeout(() => {
			// 	uni.$emit("refreshIndex");
			// 	// 监听事件
			// 	uni.navigateBack({
			// 		delta: 2,
			// 	});
			// }, 200);

			service.addInspectionLog(params).then((res) => {
				if (res.data.data) {
					uni.showToast({
						title: "添加成功",
						icon: "success",
					});
					setTimeout(() => {
						uni.$emit("refreshIndex");
						// 监听事件
						uni.navigateBack({
							delta: 2,
						});
					}, 200);
				}
			});
		})
		.catch((error) => {
			console.log("[ Upload Error ] >", error);
			uni.hideLoading();
		});

	uni.hideLoading();
};
</script>

<style lang="scss" scoped>
.add {
	padding: 24rpx;
	padding-top: 48rpx;
	background: #ffffff;
	border-top: 24rpx solid #f5f7f7;
}
.info {
	padding: 24rpx;
	border-radius: 16rpx;
	background: #f5f7f7;
}
.forms {
	padding: 24rpx;
	border-radius: 16rpx;
	background: #f5f7f7;
	margin-top: 16rpx;
}
.description {
	padding: 24rpx;
	border-radius: 16rpx;
	background: #f5f7f7;
	margin-top: 16rpx;
	padding-bottom: 12rpx;
	:deep(.uni-easyinput__content) {
		border: none !important;
		background-color: transparent !important;
	}
	:deep(.uni-easyinput__content-textarea) {
		background: none !important;
		padding: 0;
		height: 120rpx;
		min-height: 120rpx;
		padding-bottom: 36rpx;
		color: $uni-base-color !important;
	}
}
:deep(.uni-forms-item) {
	align-items: center;
	margin-bottom: 0 !important;
}
:deep(.uni-forms-item__label) {
	height: 32px !important;
}
:deep(.uni-data-checklist .checklist-group) {
	justify-content: end;
}
:deep(.uni-easyinput__content-textarea) {
	height: 60px;
	color: $uni-base-color !important;
}
:deep(.icon-del-box) {
	top: 5px;
	bottom: initial;
}
.sm {
	font-size: 24rpx;
}
.text-secondary {
	color: $uni-secondary-color;
}
.add-btn {
	width: 480rpx;
	height: 92rpx;
	line-height: 92rpx;
	margin: 0 auto;
	margin-top: 48rpx;
	background: #f6f7f7;
	border-radius: 16rpx;
	text-align: center;
	font-size: 28rpx;
	color: #6fbece;
}

.upload {
	margin-top: 16rpx;
}
:deep(.file-title) {
	color: $uni-secondary-color !important;
}
:deep(.file-count) {
	display: none !important;
}
</style>
