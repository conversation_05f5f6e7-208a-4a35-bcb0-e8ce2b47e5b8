<template>
	<div class="detail">
		<uni-collapse ref="collapse" v-model="activeNames" accordion v-if="historyRecords.length">
			<uni-collapse-item v-for="(item, index) in historyRecords" :border="false" :key="index">
				<template #title>
					<div class="flex flex-1 items-center justify-between title">
						<div class="stationName">{{ item.stationName }}</div>
						<!-- <div>{{ item.createTime }}</div> -->
						<div class="time">
							<text class="number-text" v-for="(nite, nind) in item.createTime" :key="nind">{{ nite }}</text>
						</div>
					</div>
				</template>
				<div class="bg-f5 px-3 py-2.5 rounded-lg collapse-content">
					<div class="list list-item">
						<span class="text-base-text">巡检人员：</span>
						<span>{{ item.disposeStaffName }}</span>
					</div>
					<div class="list list-item">
						<span class="text-base-text">巡检结果：</span>
						<span>{{ getErrorText(item.inspectionItems) }}</span>
					</div>
					<div class="list">
						<div class="text-base-text">巡检项目：</div>
						<div class="item" v-for="(ite, ind) in item.inspectionItems" :key="ind">
							<div class="flex items-center justify-between leading-5.5">
								<div>{{ getName(ite.item) }}</div>
								<div class="status">
									<dictionary :statusOptions="errorOptions" :value="String(ite.status)" />
								</div>
							</div>
							<div v-if="ite.status == 2 && ite.description" class="desc" style="background: rgba(111, 190, 206, 0.1); border: 1px solid rgba(151, 151, 151, 0.1); color: rgba(111, 190, 206, 1)">
								{{ ite.description }}
							</div>
						</div>
					</div>
					<div class="list list-desc">
						<div class="text-secondar-text flex items-center">
							<img src="@/static/desc.svg" class="icon" alt="" srcset="" />
							<span>补充说明</span>
						</div>
						<div class="p-4 bg-ff rounded-lg mt-2 description">
							{{ item.description || "-" }}
						</div>
					</div>
					<div class="list list-pic flex" v-if="item.files && item.files.length">
						<!-- <div class="text-secondar-text w-20">:</div> -->
						<div class="flex mt-2 gap-2 flex-wrap flex-1 w-0 imgs-box">
							<uni-file-picker :sizeType="['compressed']" title="图片说明" @select="selectFile" v-model="item.files" :readonly="true" :disabled="true"></uni-file-picker>
						</div>
					</div>
					<!-- <empty v-if="!item.files || item.files.length == 0" value="未上传图片" /> -->
				</div>
			</uni-collapse-item>
		</uni-collapse>
		<empty v-if="historyRecords.length == 0" isPage tips="该设备没有巡检记录" />
	</div>
</template>

<script setup>
import { ref, reactive, nextTick, onMounted } from "vue";
import { onShow } from "@dcloudio/uni-app";
import service from "../apiService/device";
import Dictionary from "@/components/dictionary.vue";
import empty from "@/components/empty.vue";
const pageInfo = ref();
// 获取页面传递的数据
onShow(async () => {
	const pages = getCurrentPages();
	const options = pages[pages.length - 1].options;
	pageInfo.value = options;
});

const errorOptions = [
	{
		value: "2",
		label: "异常",
		color: "#595959",
		iconColor: "#EA0C28",
		colorOffset: "#FF4D4F",
		backGroundColor: "#EA0C28",
	},
	{
		value: "1",
		label: "正常",
		color: "#595959",
		iconColor: "#33BE4F",
		colorOffset: "#5AD8A6",
		backGroundColor: "#33BE4F",
	},
];
const activeNames = ref("0");
const historyRecords = ref([]);
const selectedIte = ref({});
const collapse = ref();
const getDetail = async () => {
	let res = await service.getInspectionLogPage({
		current: 1,
		size: 100,
		deviceId: pageInfo.value.deviceId,
	});
	historyRecords.value = res.data.data.records.map((item) => {
		return {
			...item,
			files: item.files
				? item.files.map((ite) => {
						return {
							name: ite.fileId,
							url: ite.fileVisitUrl,
						};
				  })
				: null,
			createTime: item.createTime.split(" ")[0],
		};
	});
	if (historyRecords.value.length > 0) {
		setTimeout(() => {
			// collapse.value.open(0);
			activeNames.value = "0";
		});
	} else {
		setTimeout(() => {
			// collapse.value.open(0);
			activeNames.value = "-1";
		});
	}
};
const options = {
	overallSummary: "总体概览",
	batteryCompartment: "电池仓",
	highVoltageCabinet: "高压箱",
	combiner: "汇流柜",
	ems: "能量管理系统",
	pcs: "逆变器",
	bms: "电池系统",
	fireProtection: "消防系统",
	refrigerator: "液冷机",
};
const getName = (val) => {
	return options[val];
};
const getErrorText = (inspectionItems) => {
	if (inspectionItems.every((i) => i.status == 2)) {
		return "全部异常";
	}
	if (inspectionItems.some((i) => i.status == 2)) {
		return "部分异常";
	}
	return "正常";
};
onMounted(async () => {
	await getDetail();
});
</script>

<style lang="scss" scoped>
.detail {
	padding: 24rpx;
	padding-top: 48rpx;
	background: #ffffff;
	border-top: 24rpx solid #f5f7f7;
}
.text-secondar-text {
	color: $uni-secondary-color;
}
.text-base-text {
	color: $uni-base-color;
}
.w-18 {
	width: 144rpx;
}
.list {
	line-height: 44rpx;

	& + .list {
		margin-top: 16rpx;
	}
	& + .list-pic {
		margin-top: 24rpx;
	}
}
.list-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.item {
	margin-top: 16rpx;
}
.desc {
	padding: 24rpx;
	margin-top: 4rpx;
	word-break: break-all;
}
.description {
	word-break: break-all;
}
.title {
	line-height: 92rpx;
	.number-text {
		display: inline-block;
		width: 1ch; /* 根据需要调整 ch 单位的大小 */
	}
}
.w-20 {
	width: 140rpx;
}
.imgs-box {
	gap: 24rpx;
	padding-bottom: 24rpx;
	.imgs {
		width: 168rpx;
		height: 168rpx;
		border: 1px solid #f5f7f7;
	}
	.img {
		width: 100%;
		height: 100%;
	}
}
.collapse-content {
	padding: 20rpx 24rpx;
	border-radius: 16rpx;
	background: #f5f5f5;
}
.stationName {
	flex: 1;
	width: 0;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.status {
	color: $uni-secondary-color;
}
.list-desc {
	padding: 30rpx 24rpx;
	background: #fff;
	border-radius: 16rpx;
	.icon {
		width: 44rpx;
		height: 44rpx;
	}
}
:deep(.file-count) {
	display: none;
}
</style>
